import { useDispatch, useSelector } from "react-redux";
import { jwtDecode } from "jwt-decode";
import { logout, setCredentials } from "../services/authSlice";
import { useEffect, useState } from "react";
import { useGetUserDataMutation } from "../services/authApi";

const useAuth = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const { user, isAuthenticated, accessToken, refreshToken } = useSelector(
    (state) => state.auth,
  );
  const storedAccessToken = localStorage.getItem("accessToken");
  const storedRefreshToken = localStorage.getItem("refreshToken");

  const [getUserData] = useGetUserDataMutation();

  const isTokenValid = (token) => {
    if (!token) return false;
    try {
      const { exp } = jwtDecode(token);
      return exp * 1000 > Date.now();
    } catch {
      return false;
    }
  };

  useEffect(() => {
    if (!isTokenValid(storedAccessToken)) {
      dispatch(logout());
      setLoading(false);
      return;
    }

    if (!user && storedAccessToken) {
      getUserData()
        .then((fetchedUser) => {
          dispatch(
            setCredentials({
              user: fetchedUser.data.data,
              accessToken: storedAccessToken,
              refreshToken: storedRefreshToken,
              isAuthenticated: true,
            }),
          );
          setLoading(false);
        })
        .catch(() => {
          dispatch(logout());
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, [user, storedAccessToken, dispatch, getUserData]);

  return {
    isAuthenticated: isAuthenticated || !!storedAccessToken,
    user,
    accessToken: accessToken || storedAccessToken,
    accessToken: refreshToken || storedRefreshToken,
    loading,
  };
};

export default useAuth;
