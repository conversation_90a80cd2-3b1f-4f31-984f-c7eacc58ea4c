const appUrl = import.meta.env.VITE_APP_URL;
import axios from "axios";

export function handleFileWithVideoCheck({
  file,
  maxDuration = 30,
  setValue,
  setErrorMessage,
}) {
  if (!file) return;

  const isVideo = file.type.startsWith("video/");

  if (isVideo) {
    const video = document.createElement("video");
    video.preload = "metadata";

    video.onloadedmetadata = function () {
      window.URL.revokeObjectURL(video.src);
      const duration = video.duration;

      if (duration > maxDuration) {
        setErrorMessage("Video duration must be less than 30 seconds");
        setValue("ad_media", null);
      } else {
        setValue("ad_media", file);
      }
    };

    video.onerror = function () {
      setErrorMessage("Invalid video file");
      setValue("ad_media", null);
    };

    video.src = URL.createObjectURL(file);
  } else {
    setValue("ad_media", file);
  }
}

export async function uploadToS3(
  file,
  accessToken,
  getPresignedUrlEndpoint = "/media/generate-presigned-url",
) {
  if (!file) throw new Error("No file provided for upload");
  const res = await fetch(`${appUrl}${getPresignedUrlEndpoint}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: accessToken ? `Bearer ${accessToken}` : "",
    },
    body: JSON.stringify({ filename: file.name, type: file.type }),
  });

  // const res = await axios.post(
  //   'https://staging-api.pipaan.com/api/aws/generate-presigned-url',
  //   { files: [{ fileName: file.name, fileType: file.type }] },
  //   {
  //       headers: {
  //           'token': "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjIwLCJpYXQiOjE3NTA4NDM1MDIsImV4cCI6MTc1NjAyNzUwMiwidHlwZSI6InJlZnJlc2gifQ.7DMT0mbAU3YqOHGEnQiBc6m2vukMbTzPZd0ltcVmwJ0"
  //       }
  //   }
  // );

  // const preSignedUrls = res.data.directory;
  // console.log(preSignedUrls[0].presignedUrl);

  const response = await res.json();
  const { presignedUrl, fileUrl } = response.data;

  // const uploadResponse = await axios.put(preSignedUrls[0].presignedUrl, file, {
  //     headers: {
  //         'Content-Type': file.type,
  //         'Content-Encoding': 'binary'
  //     }
  // });
  const uploadResponse = await fetch(presignedUrl, {
    method: "PUT",
    body: file,
  });

  if (!uploadResponse.ok) {
    throw new Error(
      `Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`,
    );
  }

  return fileUrl;
}
