const appUrl = import.meta.env.VITE_APP_URL;
import axios from "axios";

export function handleFileWithVideoCheck({
  file,
  maxDuration = 30,
  setValue,
  setErrorMessage,
}) {
  if (!file) return;

  const isVideo = file.type.startsWith("video/");

  if (isVideo) {
    const video = document.createElement("video");
    video.preload = "metadata";

    video.onloadedmetadata = function () {
      window.URL.revokeObjectURL(video.src);
      const duration = video.duration;

      if (duration > maxDuration) {
        setErrorMessage("Video duration must be less than 30 seconds");
        setValue("ad_media", null);
      } else {
        setValue("ad_media", file);
      }
    };

    video.onerror = function () {
      setErrorMessage("Invalid video file");
      setValue("ad_media", null);
    };

    video.src = URL.createObjectURL(file);
  } else {
    setValue("ad_media", file);
  }
}

export async function uploadToS3(
  file,
  accessToken,
  getPresignedUrlEndpoint = "/media/generate-presigned-url",
) {
  if (!file) throw new Error("No file provided for upload");

  const res = await fetch(
    "https://pipaan.com360degree.com/api/aws/generate-presigned-url",
    {
      // const res = await fetch(`${appUrl}${getPresignedUrlEndpoint}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: accessToken ? `Bearer ${accessToken}` : "",
      },
      body: JSON.stringify({
        files: [
          {
            fileName: file.name,
            fileType: file.type,
          },
        ],
      }),
      // body: JSON.stringify({ filename: file.name, type: file.type }),
    },
  );

  const response = await res.json();
  // const { presignedUrl, fileUrl } = response.data;

  // await fetch(presignedUrl, {
  //   method: "PUT",
  //   headers: {
  //     "Content-Type": file.type,
  //     // "x-amz-acl": "public-read",
  //   },
  //   body: file,
  // });

  const { presignedUrl } = response.directory[0];

  await axios.put(presignedUrl, file, {
    headers: {
      "Content-Type": file.type,
      "Content-Encoding": "binary",
    },
  });

  return fileUrl;
}
