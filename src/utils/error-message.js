export function getErrorMessage(error) {
  if (error instanceof Error) {
    return error.message || error.name || "An error occurred";
  }

  if (typeof error === "string") {
    return error;
  }

  if (typeof error === "object" && error !== null) {
    const errorMessage = error.message;
    if (typeof errorMessage === "string") {
      return errorMessage;
    }
  }

  return `Unknown error: ${error}`;
}
