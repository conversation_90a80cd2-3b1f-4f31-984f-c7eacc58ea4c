import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { setAuthHeader } from "./apiHelpers.js";

const appUrl = import.meta.env.VITE_APP_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: appUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.accessToken;
    setAuthHeader(headers, token);
    return headers;
  },
});

export const reportApi = createApi({
  reducerPath: "reportApi",
  baseQuery,
  endpoints: (builder) => ({
    downloadReport: builder.mutation({
      query: (data) => ({
        url: "/report/download",
        method: "POST",
        body: data,
      }),

      responseHandler: async (response) => {
        const contentType = response.headers.get("Content-Type");

        if (contentType && contentType.includes("application/json")) {
          const json = await response.json();
          throw {
            name: "APIError",
            message: json?.message || "Download failed.",
            status: response.status,
            data: json,
          };
        }

        const blob = await response.blob();
        const contentDisposition = response.headers.get("Content-Disposition");
        const fileNameMatch = contentDisposition?.match(/filename="?(.+?)"?$/);
        const filename = fileNameMatch ? fileNameMatch[1] : "report";

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);

        return { success: true }; // ✅ This is good — keep it!
      },
    }),
  }),
});

export const { useDownloadReportMutation } = reportApi;
