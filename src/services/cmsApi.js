import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { setAuthHeader } from "./apiHelpers.js";

const appUrl = import.meta.env.VITE_APP_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: appUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.accessToken;
    setAuthHeader(headers, token);
    return headers;
  },
});

export const cmsApi = createApi({
  reducerPath: "cmsApi",
  baseQuery,
  endpoints: (builder) => ({
    getCustomerList: builder.mutation({
      // query: ({ search, filter, sortBy, sortOrder, page, limit }) => ({
      query: (data) => ({
        url: "/cms/list",
        method: "POST",
        body: data,
      }),
    }),
    getCustomerDetail: builder.mutation({
      query: (data) => ({
        url: "/cms/detail",
        method: "POST",
        body: data,
      }),
    }),
    createCustomer: builder.mutation({
      query: (data) => ({
        url: "/cms/create",
        method: "POST",
        body: data,
      }),
    }),
    editCustomer: builder.mutation({
      query: (data) => ({
        url: "/cms/edit",
        method: "POST",
        body: data,
      }),
    }),
    deleteCustomer: builder.mutation({
      query: (data) => ({
        url: "/cms/delete",
        method: "POST",
        body: data,
      }),
    }),
  }),
});

export const {
  useGetCustomerDetailMutation,
  useGetCustomerListMutation,
  useCreateCustomerMutation,
  useEditCustomerMutation,
  useDeleteCustomerMutation,
} = cmsApi;
