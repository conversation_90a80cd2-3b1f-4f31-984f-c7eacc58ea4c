import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { setAuthHeader } from "./apiHelpers.js";

const appUrl = import.meta.env.VITE_APP_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: appUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.accessToken;
    setAuthHeader(headers, token);
    return headers;
  },
});

export const roleApi = createApi({
  reducerPath: "roleApi",
  baseQuery,
  endpoints: (builder) => ({
    getRoleList: builder.mutation({
      query: () => ({
        url: "/role/list",
        method: "POST",
      }),
    }),
  }),
});

export const { useGetRoleListMutation } = roleApi;
