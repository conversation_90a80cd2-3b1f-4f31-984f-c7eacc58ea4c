import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { setAuthHeader } from "./apiHelpers.js";

const appUrl = import.meta.env.VITE_APP_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: appUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.accessToken;
    setAuthHeader(headers, token);
    return headers;
  },
});

export const roleApi = createApi({
  reducerPath: "roleApi",
  baseQuery,
  endpoints: (builder) => ({
    getRoleList: builder.mutation({
      query: () => ({
        url: "/role/list",
        method: "POST",
      }),
    }),
    getPermissionList: builder.mutation({
      query: () => ({
        url: "/permission/list",
        method: "POST",
      }),
    }),
    getRolePermissions: builder.mutation({
      query: () => ({
        url: "/role/permissions",
        method: "POST",
      }),
    }),
    updateRolePermissions: builder.mutation({
      query: (data) => ({
        url: "/role/permissions/update",
        method: "POST",
        body: data,
      }),
    }),
    createRole: builder.mutation({
      query: (data) => ({
        url: "/role/create",
        method: "POST",
        body: data,
      }),
    }),
    updateRole: builder.mutation({
      query: (data) => ({
        url: "/role/update",
        method: "POST",
        body: data,
      }),
    }),
    deleteRole: builder.mutation({
      query: (data) => ({
        url: "/role/delete",
        method: "POST",
        body: data,
      }),
    }),
  }),
});

export const {
  useGetRoleListMutation,
  useGetPermissionListMutation,
  useGetRolePermissionsMutation,
  useUpdateRolePermissionsMutation,
  useCreateRoleMutation,
  useUpdateRoleMutation,
  useDeleteRoleMutation,
} = roleApi;
