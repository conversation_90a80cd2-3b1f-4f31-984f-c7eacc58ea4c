import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { setAuthHeader } from "./apiHelpers.js";

const appUrl = import.meta.env.VITE_APP_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: appUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.accessToken;
    setAuthHeader(headers, token);
    return headers;
  },
});

export const dashboardApi = createApi({
  reducerPath: "dashboardApi",
  baseQuery,
  endpoints: (builder) => ({
    // Dashboard data fetched via GET request
    getDashboardData: builder.mutation({
      query: () => ({
        url: "/dashboard",
        method: "POST",
      }),
    }),
    // Dashboard post details via POST request
    getDashboardPostDetails: builder.mutation({
      query: () => ({
        url: "/dashboard/post",
        method: "POST",
      }),
    }),
  }),
});

export const {
  useGetDashboardDataMutation,
  useGetDashboardPostDetailsMutation,
} = dashboardApi;
