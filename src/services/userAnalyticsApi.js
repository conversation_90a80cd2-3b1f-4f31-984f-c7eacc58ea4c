import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { setAuthHeader } from "./apiHelpers.js";

const appUrl = import.meta.env.VITE_APP_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: appUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.accessToken;
    setAuthHeader(headers, token);
    return headers;
  },
});

export const userAnalyticsApi = createApi({
  reducerPath: "userAnalyticsApi",
  baseQuery,
  endpoints: (builder) => ({
    getUserAnalytics: builder.mutation({
      query: (data) => ({
        url: "/analytics/user-analytics",
        method: "POST",
        body: data,
      }),
    }),
  }),
});

export const { useGetUserAnalyticsMutation } = userAnalyticsApi;
