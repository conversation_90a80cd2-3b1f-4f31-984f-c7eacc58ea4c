import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { setAuthHeader } from "./apiHelpers.js";

const appUrl = import.meta.env.VITE_APP_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: appUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.accessToken;
    setAuthHeader(headers, token);
    return headers;
  },
});

export const analyticsApi = createApi({
  reducerPath: "analyticsApi",
  baseQuery,
  endpoints: (builder) => ({
    getLiveMetrics: builder.mutation({
      query: (data) => ({
        url: "/analytics/live-metrics",
        method: "POST",
        body: data,
      }),
    }),
  }),
});

export const { useGetLiveMetricsMutation } = analyticsApi;
