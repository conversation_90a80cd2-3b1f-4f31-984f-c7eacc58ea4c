import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { setAuthHeader } from "./apiHelpers.js";

const appUrl = import.meta.env.VITE_APP_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: appUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.accessToken;
    setAuthHeader(headers, token);
    return headers;
  },
});

export const adsApi = createApi({
  reducerPath: "adsApi",
  baseQuery,
  endpoints: (builder) => ({
    getAdsList: builder.mutation({
      // query: ({ search, filter, sortBy, sortOrder, page, limit }) => ({
      query: (data) => ({
        url: "/ads/list",
        method: "POST",
        body: data,
      }),
    }),
    getAdsDetail: builder.mutation({
      query: (data) => ({
        url: "/ads/detail",
        method: "POST",
        body: data,
      }),
    }),
    createAds: builder.mutation({
      query: (data) => ({
        url: "/ads/create",
        method: "POST",
        body: data,
      }),
    }),
    editAds: builder.mutation({
      query: (data) => ({
        url: "/ads/edit",
        method: "POST",
        body: data,
      }),
    }),
    deleteAds: builder.mutation({
      query: (data) => ({
        url: "/ads/delete",
        method: "POST",
        body: data,
      }),
    }),
  }),
});

export const {
  useGetAdsDetailMutation,
  useGetAdsListMutation,
  useCreateAdsMutation,
  useEditAdsMutation,
  useDeleteAdsMutation,
} = adsApi;
