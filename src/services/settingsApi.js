// features/settings/settingsApi.js
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { setAuthHeader } from "./apiHelpers";

const appUrl = import.meta.env.VITE_APP_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: appUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.accessToken;
    setAuthHeader(headers, token);
    return headers;
  },
});

export const settingsApi = createApi({
  reducerPath: "settingsApi",
  baseQuery,
  tagTypes: ["Settings"],
  endpoints: (builder) => ({
    getSettings: builder.mutation({
      query: () => ({
        url: "/settings",
        method: "POST",
      }),
      invalidatesTags: ["Settings"],
    }),
    editSettings: builder.mutation({
      query: (data) => ({
        url: "/settings/edit",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Settings"],
    }),
  }),
});

export const { useGetSettingsMutation, useEditSettingsMutation } = settingsApi;