import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { setAuthHeader } from "./apiHelpers.js";

const appUrl = import.meta.env.VITE_APP_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: appUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.accessToken;
    setAuthHeader(headers, token);
    return headers;
  },
});

export const donationsApi = createApi({
  reducerPath: "donationsApi",
  baseQuery,
  endpoints: (builder) => ({
    // POST: Fetch all donations
    getDonations: builder.mutation({
      query: () => ({
        url: "/donations",
        method: "POST",
      }),
    }),

    // POST: Get donation details by eventId
    getDonationDetail: builder.mutation({
      query: (body) => ({
        url: "/donations/detail",
        method: "POST",
        body, // expects { eventId }
      }),
    }),

    // POST: Edit a donation
    editDonation: builder.mutation({
      query: (body) => ({
        url: "/donations/edit",
        method: "POST",
        body, // expects { eventId, title, goal, startDate, endDate }
      }),
    }),
  }),
});

// Export hooks
export const {
  useGetDonationsMutation,
  useGetDonationDetailMutation,
  useEditDonationMutation,
} = donationsApi;
