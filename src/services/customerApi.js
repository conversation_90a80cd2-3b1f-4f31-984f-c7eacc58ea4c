import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { setAuthHeader } from "./apiHelpers.js";

const appUrl = import.meta.env.VITE_APP_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: appUrl,
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.accessToken;
    setAuthHeader(headers, token);
    return headers;
  },
});

export const customerApi = createApi({
  reducerPath: "customerApi",
  baseQuery,
  endpoints: (builder) => ({
    getCustomerList: builder.mutation({
      // query: ({ search, filter, sortBy, sortOrder, page, limit }) => ({
      query: (data) => ({
        url: "/customer/list",
        method: "POST",
        body: data,
      }),
    }),
    getCustomerDetail: builder.mutation({
      query: (data) => ({
        url: "/customer/detail",
        method: "POST",
        body: data,
      }),
    }),
    createCustomer: builder.mutation({
      query: (data) => ({
        url: "/customer/create",
        method: "POST",
        body: data,
      }),
    }),
    editCustomer: builder.mutation({
      query: (data) => ({
        url: "/customer/edit",
        method: "POST",
        body: data,
      }),
    }),
    deleteCustomer: builder.mutation({
      query: (data) => ({
        url: "/customer/delete",
        method: "POST",
        body: data,
      }),
    }),
  }),
});

export const {
  useGetCustomerDetailMutation,
  useGetCustomerListMutation,
  useCreateCustomerMutation,
  useEditCustomerMutation,
  useDeleteCustomerMutation,
} = customerApi;
