import CssBaseline from "@mui/material/CssBaseline";
import { ThemeProvider as ThemeVarsProvider } from "@mui/material/styles";
import { createTheme } from "./create-theme";
import { Rtl } from "./right-to-left";

export function ThemeProvider({ themeOverrides, children, ...other }) {
  const theme = createTheme({
    themeOverrides,
  });

  return (
    <ThemeVarsProvider disableTransitionOnChange theme={theme} {...other}>
      <CssBaseline />
      <Rtl direction="ltr">{children}</Rtl>
    </ThemeVarsProvider>
  );
}
