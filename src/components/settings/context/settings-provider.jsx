import { isEqual } from "es-toolkit";
import { useLocalStorage } from "minimal-shared/hooks";
import { useMemo, useState, useEffect, useCallback } from "react";
import { getStorage as getStorageValue } from "minimal-shared/utils";
import { SettingsContext } from "./settings-context";
import { SETTINGS_STORAGE_KEY } from "../settings-config";

export function SettingsProvider({
  children,
  defaultSettings,
  storageKey = SETTINGS_STORAGE_KEY,
}) {
  const { state, setState, resetState, setField } = useLocalStorage(
    storageKey,
    defaultSettings,
  );

  const [openDrawer, setOpenDrawer] = useState(false);

  const onToggleDrawer = useCallback(() => {
    setOpenDrawer((prev) => !prev);
  }, []);

  const onCloseDrawer = useCallback(() => {
    setOpenDrawer(false);
  }, []);

  const canReset = !isEqual(state, defaultSettings);

  const onReset = useCallback(() => {
    resetState(defaultSettings);
  }, [defaultSettings, resetState]);

  // Version check and reset handling
  useEffect(() => {
    const storedValue = getStorageValue(storageKey);

    if (storedValue) {
      try {
        if (
          !storedValue.version ||
          storedValue.version !== defaultSettings.version
        ) {
          onReset();
        }
      } catch {
        onReset();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const memoizedValue = useMemo(
    () => ({
      canReset,
      onReset,
      openDrawer,
      onCloseDrawer,
      onToggleDrawer,
      state,
      setState,
      setField,
    }),
    [
      canReset,
      onReset,
      openDrawer,
      onCloseDrawer,
      onToggleDrawer,
      state,
      setField,
      setState,
    ],
  );

  return (
    <SettingsContext.Provider value={memoizedValue}>
      {children}
    </SettingsContext.Provider>
  );
}
