import { forwardRef } from "react";
import { mergeClasses } from "minimal-shared/utils";

import Tooltip from "@mui/material/Tooltip";
import { styled } from "@mui/material/styles";
import ButtonBase from "@mui/material/ButtonBase";

import { Iconify } from "../../iconify";
import { createNavItem } from "../utils";
import { navItemStyles, navSectionClasses } from "../styles";

// ----------------------------------------------------------------------

export const NavItem = forwardRef((props, ref) => {
  const {
    path,
    icon,
    info,
    title,
    caption,
    /********/
    open,
    active,
    disabled,
    /********/
    depth,
    render,
    hasChild,
    slotProps,
    className,
    externalLink,
    enabledRootRedirect,
    ...other
  } = props;

  const navItem = createNavItem({
    path,
    icon,
    info,
    depth,
    render,
    hasChild,
    externalLink,
    enabledRootRedirect,
  });

  const ownerState = {
    open,
    active,
    disabled,
    variant: navItem.rootItem ? "rootItem" : "subItem",
  };

  return (
    <ItemRoot
      ref={ref}
      aria-label={title}
      {...ownerState}
      {...navItem.baseProps}
      className={mergeClasses([navSectionClasses.item.root, className], {
        [navSectionClasses.state.open]: open,
        [navSectionClasses.state.active]: active,
        [navSectionClasses.state.disabled]: disabled,
      })}
      sx={slotProps?.sx}
      {...other}
    >
      {icon && (
        <ItemIcon
          {...ownerState}
          className={navSectionClasses.item.icon}
          sx={slotProps?.icon}
        >
          {navItem.renderIcon}
        </ItemIcon>
      )}

      {title && (
        <ItemTitle
          {...ownerState}
          className={navSectionClasses.item.title}
          sx={slotProps?.title}
        >
          {title}
        </ItemTitle>
      )}

      {caption && (
        <Tooltip title={caption} arrow>
          <ItemCaptionIcon
            {...ownerState}
            icon="eva:info-outline"
            className={navSectionClasses.item.caption}
            sx={slotProps?.caption}
          />
        </Tooltip>
      )}

      {info && (
        <ItemInfo
          {...ownerState}
          className={navSectionClasses.item.info}
          sx={slotProps?.info}
        >
          {navItem.renderInfo}
        </ItemInfo>
      )}

      {hasChild && (
        <ItemArrow
          {...ownerState}
          icon={
            navItem.subItem
              ? "eva:arrow-ios-forward-fill"
              : "eva:arrow-ios-downward-fill"
          }
          className={navSectionClasses.item.arrow}
          sx={slotProps?.arrow}
        />
      )}
    </ItemRoot>
  );
});

// ----------------------------------------------------------------------

const shouldForwardProp = (prop) =>
  !["open", "active", "disabled", "variant", "sx"].includes(prop);

/**
 * @slot root
 */
const ItemRoot = styled(ButtonBase, { shouldForwardProp })(({
  active,
  open,
  theme,
}) => {
  const rootItemStyles = {
    padding: "var(--nav-item-root-padding)",
    minHeight: "var(--nav-item-root-height)",
    ...(open && {
      color: "var(--nav-item-root-open-color)",
      backgroundColor: "var(--nav-item-root-open-bg)",
    }),
    ...(active && {
      color: "var(--nav-item-root-active-color)",
      backgroundColor: "var(--nav-item-root-active-bg)",
      "&:hover": { backgroundColor: "var(--nav-item-root-active-hover-bg)" },
      ...theme.applyStyles("dark", {
        color: "var(--nav-item-root-active-color-on-dark)",
      }),
    }),
  };

  const subItemStyles = {
    padding: "var(--nav-item-sub-padding)",
    minHeight: "var(--nav-item-sub-height)",
    color: theme.vars.palette.text.secondary,
    ...(open && {
      color: "var(--nav-item-sub-open-color)",
      backgroundColor: "var(--nav-item-sub-open-bg)",
    }),
    ...(active && {
      color: "var(--nav-item-sub-active-color)",
      backgroundColor: "var(--nav-item-sub-active-bg)",
    }),
  };

  return {
    width: "100%",
    flexShrink: 0,
    color: "var(--nav-item-color)",
    borderRadius: "var(--nav-item-radius)",
    "&:hover": { backgroundColor: "var(--nav-item-hover-bg)" },
    variants: [
      { props: { variant: "rootItem" }, style: rootItemStyles },
      { props: { variant: "subItem" }, style: subItemStyles },
      { props: { disabled: true }, style: navItemStyles.disabled },
    ],
  };
});

/**
 * @slot icon
 */
const ItemIcon = styled("span", { shouldForwardProp })(() => ({
  ...navItemStyles.icon,
  width: "var(--nav-icon-size)",
  height: "var(--nav-icon-size)",
  margin: "var(--nav-icon-root-margin)",
  variants: [
    {
      props: { variant: "subItem" },
      style: { margin: "var(--nav-icon-sub-margin)" },
    },
  ],
}));

/**
 * @slot title
 */
const ItemTitle = styled("span", { shouldForwardProp })(({ theme }) => ({
  ...navItemStyles.title(theme),
  ...theme.typography.body2,
  fontWeight: theme.typography.fontWeightMedium,
  variants: [
    {
      props: { active: true },
      style: { fontWeight: theme.typography.fontWeightSemiBold },
    },
  ],
}));

/**
 * @slot caption icon
 */
const ItemCaptionIcon = styled(Iconify, { shouldForwardProp })(({ theme }) => ({
  ...navItemStyles.captionIcon,
  color: "var(--nav-item-caption-color)",
  variants: [
    {
      props: { variant: "rootItem" },
      style: { marginLeft: theme.spacing(0.75) },
    },
  ],
}));

/**
 * @slot info
 */
const ItemInfo = styled("span", { shouldForwardProp })(({ theme }) => ({
  ...navItemStyles.info,
}));

/**
 * @slot arrow
 */
const ItemArrow = styled(Iconify, { shouldForwardProp })(({ theme }) => ({
  ...navItemStyles.arrow(theme),
  variants: [
    {
      props: { variant: "subItem" },
      style: { marginRight: theme.spacing(-0.5) },
    },
  ],
}));
