import { Link } from "react-router-dom";
import { routes } from "../../routes/routes";

function Sidebar() {
  return (
    <nav className="sidebar">
      <ul>
        {routes.map((route, index) =>
          route.path !== "*" ? (
            <li key={index}>
              <Link to={route.path}>
                {route.icon && <span>{route.icon}</span>} {route.path}
              </Link>
            </li>
          ) : null,
        )}
      </ul>
    </nav>
  );
}

export default Sidebar;
