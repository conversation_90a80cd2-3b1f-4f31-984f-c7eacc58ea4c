import { Avatar, Box, Link, ListItemIcon, MenuItem } from "@mui/material";
import { fDate, fTime } from "../../utils/format-time";
import { Label } from "../label";
import { forwardRef } from "react";
import { RouterLink } from "../../components/routes/components";

export function RenderDateCell({ date }) {
  return date ? (
    <Box sx={{ display: "flex", gap: "4px" }}>
      <span>{fDate(date)}</span>
      <span>{fTime(date)}</span>
    </Box>
  ) : (
    <span>{"----"}</span>
  );
}

export function RenderStatus({ status }) {
  return (
    <Label
      variant="soft"
      color={status?.toLowerCase() === "active" ? "success" : "default"}
    >
      {status}
    </Label>
  );
}

export function RenderText({ text }) {
  return <span>{text ? text : "----"}</span>;
}

export function RenderImage({ title, image }) {
  return (
    <Avatar
      alt={title}
      src={image}
      variant="rounded"
      sx={{ width: 64, height: 64 }}
    />
  );
}

export const GridActionsLinkItem = forwardRef((props, ref) => {
  const { href, label, icon, sx } = props;

  return (
    <MenuItem ref={ref} sx={sx}>
      <Link
        component={RouterLink}
        href={href}
        underline="none"
        color="inherit"
        sx={{ width: 1, display: "flex", alignItems: "center" }}
      >
        {icon && <ListItemIcon>{icon}</ListItemIcon>}
        {label}
      </Link>
    </MenuItem>
  );
});
