import { useEffect, useRef } from "react";
import { TableToolbar } from "./topbar";
import { Box, Select, MenuItem, InputLabel, FormControl } from "@mui/material";
import {
  GridToolbarExport,
  GridToolbarContainer,
  GridToolbarQuickFilter,
  GridToolbarColumnsButton,
} from "@mui/x-data-grid";

export function CustomToolbar({ filters, setFilterButtonEl }) {
  const anchorRef = useRef(null);

  useEffect(() => {
    if (setFilterButtonEl && anchorRef.current) {
      setFilterButtonEl(anchorRef.current);
    }
  }, [setFilterButtonEl]);
  return (
    <>
      <GridToolbarContainer>
        <TableToolbar filters={filters} />

        <FormControl
          variant="outlined"
          size="medium"
          sx={{
            minWidth: 200,
            "& .MuiOutlinedInput-root": {
              height: 52,
            },
          }}
        >
          <InputLabel>Status</InputLabel>
          <Select
            label="Status"
            value={filters.state.status || ""}
            onChange={(e) => filters.setState({ status: e.target.value })}
          >
            <MenuItem value="">All</MenuItem>
            <MenuItem value="Active">Active</MenuItem>
            <MenuItem value="Inactive">Inactive</MenuItem>
          </Select>
        </FormControl>

        <GridToolbarQuickFilter />

        <Box
          sx={{
            gap: 1,
            flexGrow: 1,
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-end",
          }}
        >
          <span ref={anchorRef}>
            <GridToolbarColumnsButton />
          </span>
          <GridToolbarExport />
        </Box>
      </GridToolbarContainer>
    </>
  );
}
