import { useCallback, useState } from "react";
import { EmptyContent } from "../empty-content";
import { useSetState } from "minimal-shared/hooks";
import { CustomToolbar } from "./custom-toolbar";
import { DataGrid, gridClasses } from "@mui/x-data-grid";

const HIDE_COLUMNS = {};
const HIDE_COLUMNS_TOGGLABLE = ["actions"];

export function TableData({ tableData, columns, productsLoading }) {
  const [selectedRowIds, setSelectedRowIds] = useState([]);
  const [filterButtonEl, setFilterButtonEl] = useState(null);

  const filters = useSetState({});
  const { state: currentFilters } = filters;

  const [columnVisibilityModel, setColumnVisibilityModel] =
    useState(HIDE_COLUMNS);

  const getTogglableColumns = () =>
    columns
      .filter((column) => !HIDE_COLUMNS_TOGGLABLE.includes(column.field))
      .map((column) => column.field);

  const CustomToolbarCallback = useCallback(
    () => (
      <CustomToolbar
        filters={filters}
        setFilterButtonEl={setFilterButtonEl}
        filteredResults={tableData?.length}
      />
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [currentFilters, selectedRowIds],
  );

  const filteredRows = tableData.filter((row) => {
    if (!currentFilters.status) return true;
    return row.status === currentFilters.status;
  });

  return (
    <DataGrid
      rows={filteredRows}
      columns={columns}
      loading={productsLoading}
      getRowHeight={() => "auto"}
      pageSizeOptions={[5, 10, 20, { value: -1, label: "All" }]}
      initialState={{ pagination: { paginationModel: { pageSize: 10 } } }}
      onRowSelectionModelChange={(newSelectionModel) =>
        setSelectedRowIds(newSelectionModel)
      }
      columnVisibilityModel={columnVisibilityModel}
      onColumnVisibilityModelChange={(newModel) =>
        setColumnVisibilityModel(newModel)
      }
      slots={{
        toolbar: CustomToolbarCallback,
        noRowsOverlay: () => <EmptyContent />,
        noResultsOverlay: () => <EmptyContent title="No results found" />,
      }}
      slotProps={{
        toolbar: { setFilterButtonEl },
        panel: { anchorEl: filterButtonEl },
        columnsManagement: { getTogglableColumns },
      }}
      sx={{
        "& .MuiDataGrid-main": {
          padding: "10px",
        },
        [`& .${gridClasses.cell}`]: {
          alignItems: "center",
          display: "inline-flex",
        },
        "& .MuiDataGrid-container--top [role=row]": {
          backgroundColor: "#d4dce2",
        },
      }}
    />
  );
}
