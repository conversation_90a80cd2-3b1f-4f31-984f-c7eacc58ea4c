import { Box, Button, styled, Typography } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";

const VisuallyHiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});

export default function InputFileUpload({
  onChange,
  name,
  label,
  value,
  type = "all",
}) {
  const getAccept = () => {
    if (type === "image") return "image/*";
    if (type === "video") return "video/*";
    return "image/*,video/*";
  };

  return (
    <Box>
      <Button
        component="label"
        role={undefined}
        variant="contained"
        tabIndex={-1}
        startIcon={<CloudUploadIcon />}
      >
        {label || "Upload File"}
        <VisuallyHiddenInput
          type="file"
          onChange={onChange}
          name={name}
          accept={getAccept()}
        />
      </Button>
      {value && (
        <Typography variant="body2" mt={1}>
          {value.name}
        </Typography>
      )}
    </Box>
  );
}
