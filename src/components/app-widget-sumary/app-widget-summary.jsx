import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import { useTheme } from "@mui/material/styles"; // ✅ added
import { fNumber, fPercent } from "../../utils/format-number";
import { Iconify } from "../../components/iconify";
import { Chart, useChart } from "../../components/chart";

export function AppWidgetSummary({
  title,
  percent,
  total,
  chart = {},
  sx,
  ...other
}) {
  const theme = useTheme();

  const chartColors = chart.colors || [theme.palette.primary.main]; // ✅ safe fallback

  const chartOptions = useChart({
    chart: { sparkline: { enabled: true } },
    colors: chartColors,
    stroke: { width: 0 },
    xaxis: { categories: chart.categories || [] },
    tooltip: {
      y: {
        formatter: (value) => fNumber(value),
        title: { formatter: () => "" },
      },
    },
    plotOptions: { bar: { borderRadius: 1.5, columnWidth: "64%" } },
    ...(chart.options || {}),
  });

  const renderTrending = () => (
    <Box sx={{ gap: 0.5, display: "flex", alignItems: "center" }}>
      <Iconify
        width={24}
        icon={
          percent < 0
            ? "solar:double-alt-arrow-down-bold-duotone"
            : "solar:double-alt-arrow-up-bold-duotone"
        }
        sx={{
          flexShrink: 0,
          color: percent < 0 ? "error.main" : "success.main",
        }}
      />
      <Box component="span" sx={{ typography: "subtitle2" }}>
        {percent > 0 && "+"}
        {fPercent(percent)}
      </Box>
      <Box
        component="span"
        sx={{ typography: "body2", color: "text.secondary" }}
      >
        last 7 days
      </Box>
    </Box>
  );

  return (
    <Card
      sx={[
        {
          p: 3,
          display: "flex",
          alignItems: "center",
          overflow: "unset",
          zIndex: "unset",
        },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
      {...other}
    >
      <Box sx={{ flexGrow: 1 }}>
        <Box sx={{ typography: "subtitle2" }}>{title}</Box>

        <Box sx={{ mt: 1.5, mb: 1, typography: "h3" }}>{fNumber(total)}</Box>

        {renderTrending()}
      </Box>

      <Chart
        type="bar"
        series={[{ data: chart.series || [] }]}
        options={chartOptions}
        sx={{ width: 60, height: 40 }}
      />
    </Card>
  );
}
