import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App";
import { Outlet, RouterProvider, createBrowserRouter } from "react-router";
import { ErrorBoundary } from "./components/routes/components";
import { routes } from "./routes";
import { Provider } from "react-redux";
import store from "./store";

const router = createBrowserRouter([
  {
    Component: () => (
      <Provider store={store}>
        <App>
          <Outlet />
        </App>
      </Provider>
    ),
    errorElement: <ErrorBoundary />,
    children: routes,
  },
]);

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <RouterProvider router={router} />
  </StrictMode>,
);
