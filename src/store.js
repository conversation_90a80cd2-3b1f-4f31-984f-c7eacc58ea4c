import { configureStore } from "@reduxjs/toolkit";
import authSlice from "./services/authSlice";
import { authApi } from "./services/authApi";
import { userApi } from "./services/userApi";
import { customerApi } from "./services/customerApi";
import { adsApi } from "./services/adsApi";
import { roleApi } from "./services/roleApi";
import { userAnalyticsApi } from "./services/userAnalyticsApi";
import { dashboardApi } from "./services/dashboardApi";
import { reportApi } from "./services/reportApi";
import { analyticsApi } from "./services/analyticsApi";
import { donationsApi } from "./services/donationsApi";
import { settingsApi } from "./services/settingsApi";
import { cmsApi } from "./services/cmsApi";

export const store = configureStore({
  reducer: {
    auth: authSlice,
    [authApi.reducerPath]: authApi.reducer,
    [userApi.reducerPath]: userApi.reducer,
    [customerApi.reducerPath]: customerApi.reducer,
    [roleApi.reducerPath]: roleApi.reducer,
    [adsApi.reducerPath]: adsApi.reducer,
    [analyticsApi.reducerPath]: analyticsApi.reducer,
    [userAnalyticsApi.reducerPath]: userAnalyticsApi.reducer,
    [dashboardApi.reducerPath]: dashboardApi.reducer,
    [reportApi.reducerPath]: reportApi.reducer,
    [donationsApi.reducerPath]: donationsApi.reducer,
    [settingsApi.reducerPath]: settingsApi.reducer,
    [cmsApi.reducerPath]: cmsApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      authApi.middleware,
      userApi.middleware,
      customerApi.middleware,
      roleApi.middleware,
      adsApi.middleware,
      analyticsApi.middleware,
      userAnalyticsApi.middleware,
      dashboardApi.middleware,
      reportApi.middleware,
      settingsApi.middleware,
      cmsApi.middleware,
    ),
});

export default store;
