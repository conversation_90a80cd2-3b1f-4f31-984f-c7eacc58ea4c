import "./global.css";
import { defaultSettings, SettingsProvider } from "./components/settings";
import { ThemeProvider } from "./theme/theme-provider";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";

function App({ children }) {
  return (
    <>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <SettingsProvider defaultSettings={defaultSettings}>
          <ThemeProvider noSsr defaultMode="light" modeStorageKey="theme-mode">
            {children}
          </ThemeProvider>
        </SettingsProvider>
      </LocalizationProvider>
    </>
  );
}

export default App;
