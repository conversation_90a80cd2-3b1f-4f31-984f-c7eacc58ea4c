import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z as zod } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import {
  Card,
  TextField,
  Button,
  Box,
  Snackbar,
  Alert,
  CircularProgress,
  MenuItem,
  InputAdornment,
  IconButton,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import { useCreateUserMutation } from "../../services/userApi";
import { useGetRoleListMutation } from "../../services/roleApi";
import { useBoolean } from "minimal-shared/hooks";
import { Iconify } from "../../components/iconify";
import { useRouter } from "../../components/routes/hooks";

const CreateUserSchema = zod
  .object({
    first_name: zod.string().min(1, "First name is required."),
    last_name: zod.string().min(1, "Last name is required."),
    email: zod.string().email("Invalid email address."),
    mobile_number: zod.string().min(10, "Invalid phone number."),
    role: zod.coerce.number().min(1, "Role is required."),
    accountStatus: zod.enum(["Active", "Inactive"], {
      message: "Invalid status selected.",
    }),
    password: zod
      .string()
      .min(8, "Password must be at least 8 characters.")
      .max(20, "Password cannot exceed 20 characters.")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter!")
      .regex(/\d/, "Password must contain at least one number!")
      .regex(
        /[@$!%*?&]/,
        "Password must contain at least one special character!",
      ),
    confirmPassword: zod.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords must match!",
    path: ["confirmPassword"],
  });

export default function UserCreate() {
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState(false);
  const [createUser, { isLoading }] = useCreateUserMutation();
  const [getRoleList, { data: roleList, isLoading: isRoleLoading }] =
    useGetRoleListMutation();
  const showPassword = useBoolean();
  const showConfirmPassword = useBoolean();

  useEffect(() => {
    getRoleList();
  }, [getRoleList]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm({
    resolver: zodResolver(CreateUserSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      mobile_number: "",
      role: "",
      accountStatus: "Active",
      password: "",
      confirmPassword: "",
    },
  });

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const onSubmit = async (data) => {
    try {
      await createUser(data).unwrap();
      setSuccessMessage(true);
      reset();
      setTimeout(() => {
        router.push("/user");
      }, 1000);
    } catch (error) {
      setErrorMessage(error.data?.message || "Failed to create user.");
    }
  };

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Create a new User"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Admin Users", href: paths.main.user.list },
          { name: "New User" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
        }}
      >
        <Form
          methods={{ handleSubmit, register }}
          onSubmit={handleSubmit(onSubmit)}
        >
          <Box mt={3}>
            <Grid container spacing={2}>
              <Grid size={12}>
                <TextField
                  {...register("first_name")}
                  fullWidth
                  label="First Name"
                  error={!!errors.first_name}
                  helperText={errors.first_name?.message}
                />
              </Grid>
              <Grid size={12}>
                <TextField
                  {...register("last_name")}
                  fullWidth
                  label="Last Name"
                  error={!!errors.last_name}
                  helperText={errors.last_name?.message}
                />
              </Grid>
              <Grid size={12}>
                <TextField
                  {...register("email")}
                  fullWidth
                  label="Email Address"
                  type="email"
                  error={!!errors.email}
                  helperText={errors.email?.message}
                />
              </Grid>
              <Grid size={12}>
                <TextField
                  {...register("mobile_number")}
                  fullWidth
                  label="Phone Number"
                  error={!!errors.mobile_number}
                  helperText={errors.mobile_number?.message}
                />
              </Grid>
              <Grid size={12}>
                <TextField
                  select
                  {...register("role")}
                  fullWidth
                  label="Role"
                  error={!!errors.role}
                  helperText={errors.role?.message}
                  disabled={isRoleLoading}
                  value={watch("role") || ""}
                >
                  {roleList?.data?.roles?.length ? (
                    roleList.data.roles.map((role) => (
                      <MenuItem key={role.id} value={role.id}>
                        {role.roleDisplayName}
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem disabled>No roles available</MenuItem>
                  )}
                </TextField>
              </Grid>
              <Grid size={12}>
                <TextField
                  select
                  {...register("accountStatus")}
                  fullWidth
                  label="Account Status"
                  error={!!errors.accountStatus}
                  helperText={errors.accountStatus?.message}
                  value={watch("accountStatus") || "Active"}
                >
                  <MenuItem value="Active">Active</MenuItem>
                  <MenuItem value="Inactive">Inactive</MenuItem>
                </TextField>
              </Grid>
              <Grid size={12}>
                <TextField
                  {...register("password")}
                  fullWidth
                  label="Password"
                  type={showPassword.value ? "text" : "password"}
                  error={!!errors.password}
                  helperText={errors.password?.message}
                  slotProps={{
                    inputLabel: { shrink: true },
                    input: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={showPassword.onToggle}
                            edge="end"
                          >
                            <Iconify
                              icon={
                                showPassword.value
                                  ? "solar:eye-bold"
                                  : "solar:eye-closed-bold"
                              }
                            />
                          </IconButton>
                        </InputAdornment>
                      ),
                    },
                  }}
                />
              </Grid>
              <Grid size={12}>
                <TextField
                  {...register("confirmPassword")}
                  fullWidth
                  label="Confirm Password"
                  type={showConfirmPassword.value ? "text" : "password"}
                  error={!!errors.confirmPassword}
                  helperText={errors.confirmPassword?.message}
                  slotProps={{
                    inputLabel: { shrink: true },
                    input: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={showConfirmPassword.onToggle}
                            edge="end"
                          >
                            <Iconify
                              icon={
                                showConfirmPassword.value
                                  ? "solar:eye-bold"
                                  : "solar:eye-closed-bold"
                              }
                            />
                          </IconButton>
                        </InputAdornment>
                      ),
                    },
                  }}
                />
              </Grid>
              <Grid size={12} display="flex" justifyContent="flex-end">
                <Button
                  type="submit"
                  variant="contained"
                  disabled={isLoading}
                  color="primary"
                  size="large"
                  startIcon={isLoading ? <CircularProgress size={20} /> : null}
                >
                  Create User
                </Button>
              </Grid>
            </Grid>
          </Box>
        </Form>
      </Card>

      <Snackbar
        open={successMessage || !!errorMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={errorMessage ? "error" : "success"}
          variant="filled"
        >
          {errorMessage || "User created successfully."}
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
}
