import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import { Card, TextField, Box, CircularProgress } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import { useGetUserDetailMutation } from "../../services/userApi";
import { useParams } from "react-router";
import { fDate, fTime } from "../../utils/format-time";

export default function UserView() {
  const { id } = useParams();
  const [getUserDetail, { data: userData, isLoading }] =
    useGetUserDetailMutation();

  const { watch, setValue } = useForm({});

  useEffect(() => {
    getUserDetail({ userId: id });
  }, [getUserDetail, id]);

  useEffect(() => {
    if (userData?.data) {
      Object.entries(userData.data).forEach(([key, value]) => {
        setValue(key, value);
      });
    }
  }, [userData, setValue]);

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="User Details"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Admin Users", href: paths.main.user.list },
          { name: "User Details" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: isLoading ? "center" : "flex-start",
        }}
      >
        {isLoading ? (
          <CircularProgress />
        ) : (
          <Form>
            <Box mt={3}>
              <Grid container spacing={2}>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="User ID"
                    value={watch("id") || "----"}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Full Name"
                    value={`${watch("first_name") || "----"} ${watch("last_name") || ""}`}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Email Address"
                    value={watch("email") || "----"}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Phone Number"
                    value={watch("mobile_number") || "----"}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    label="Role"
                    disabled
                    value={watch("userRole.role") || "----"}
                  ></TextField>
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Account Status"
                    value={watch("status") || "----"}
                  ></TextField>
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Created date"
                    value={
                      watch("created_at")
                        ? `${fDate(watch("created_at"))} ${fTime(watch("created_at"))}`
                        : "----"
                    }
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Last Login"
                    value={
                      watch("last_login_at")
                        ? `${fDate(watch("last_login_at"))} ${fTime(watch("last_login_at"))}`
                        : "----"
                    }
                  />
                </Grid>
              </Grid>
            </Box>
          </Form>
        )}
      </Card>
    </DashboardContent>
  );
}
