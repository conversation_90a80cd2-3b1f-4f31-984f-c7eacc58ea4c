import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z as zod } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useParams } from "react-router";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import {
  Card,
  TextField,
  Button,
  Box,
  Snackbar,
  Alert,
  CircularProgress,
  MenuItem,
  InputAdornment,
  IconButton,
  Switch,
  FormControlLabel,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import {
  useEditUserMutation,
  useGetUserDetailMutation,
} from "../../services/userApi";
import { useGetRoleListMutation } from "../../services/roleApi";
import { useBoolean } from "minimal-shared/hooks";
import { Iconify } from "../../components/iconify";
import { useRouter } from "../../components/routes/hooks";

const EditUserSchema = zod
  .object({
    first_name: zod.string().min(1, "First name is required."),
    last_name: zod.string().min(1, "Last name is required."),
    email: zod.string().email("Invalid email address."),
    mobile_number: zod.string().min(10, "Invalid phone number."),
    role: zod.number().min(1, "Role is required."),
    accountStatus: zod.enum(["Active", "Inactive"], {
      message: "Invalid status selected.",
    }),
    password: zod.string().optional(),
    confirmPassword: zod.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.password || data.confirmPassword) {
      if (!data.password) {
        ctx.addIssue({
          path: ["password"],
          message: "Password is required when changing password.",
          code: "custom",
        });
      }
      if (!data.confirmPassword) {
        ctx.addIssue({
          path: ["confirmPassword"],
          message: "Confirm password is required.",
          code: "custom",
        });
      }
      if (data.password !== data.confirmPassword) {
        ctx.addIssue({
          path: ["confirmPassword"],
          message: "Passwords must match!",
          code: "custom",
        });
      }
    }
  });

export default function UserEdit() {
  const { id } = useParams();
  const router = useRouter();
  const showPassword = useBoolean();
  const showConfirmPassword = useBoolean();
  const [changePassword, setChangePassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState(false);

  const [editUser, { isLoading }] = useEditUserMutation();
  const [getRoleList, { data: roleList }] = useGetRoleListMutation();
  const [getUserDetail, { data: userData, isLoading: isUserDataLoading }] =
    useGetUserDetailMutation();

  useEffect(() => {
    getUserDetail({ userId: id });
    getRoleList();
  }, [getUserDetail, getRoleList, id]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch,
  } = useForm({
    resolver: zodResolver(EditUserSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      mobile_number: "",
      role: "",
      accountStatus: "Active",
    },
  });

  useEffect(() => {
    if (userData?.data) {
      reset({
        first_name: userData.data.first_name,
        last_name: userData.data.last_name,
        email: userData.data.email,
        mobile_number: userData.data.mobile_number,
        role: userData.data.role,
        accountStatus: userData.data.status,
      });
    }
  }, [userData, reset]);

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const onSubmit = async (data) => {
    try {
      if (!changePassword) {
        delete data.password;
        delete data.confirmPassword;
      }
      await editUser({ userId: id, ...data }).unwrap();
      setSuccessMessage(true);
      setTimeout(() => {
        router.push("/user");
      }, 1000);
    } catch (error) {
      console.error("Update Error:", error);
      setErrorMessage(error?.data?.message || "Failed to update user.");
    }
  };

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Edit User"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Admin Users", href: paths.main.user.list },
          { name: "Edit" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: isUserDataLoading ? "center" : "flex-start",
        }}
      >
        {isUserDataLoading ? (
          <CircularProgress />
        ) : (
          <Form
            methods={{ handleSubmit, register }}
            onSubmit={handleSubmit(onSubmit)}
          >
            <Box mt={3}>
              <Grid container spacing={2}>
                <Grid size={12}>
                  <TextField
                    {...register("first_name")}
                    fullWidth
                    label="First Name"
                    error={!!errors.first_name}
                    helperText={errors.first_name?.message}
                    value={watch("first_name") || ""}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    {...register("last_name")}
                    fullWidth
                    label="Last Name"
                    error={!!errors.last_name}
                    helperText={errors.last_name?.message}
                    value={watch("last_name") || ""}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    {...register("email")}
                    fullWidth
                    label="Email Address"
                    type="email"
                    disabled
                    error={!!errors.email}
                    helperText={errors.email?.message}
                    value={watch("email") || ""}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    {...register("mobile_number")}
                    fullWidth
                    label="Phone Number"
                    error={!!errors.mobile_number}
                    helperText={errors.mobile_number?.message}
                    value={watch("mobile_number") || ""}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    select
                    {...register("role")}
                    fullWidth
                    label="Role"
                    error={!!errors.role}
                    helperText={errors.role?.message}
                    value={watch("role") || ""}
                  >
                    {roleList?.data?.roles?.length ? (
                      roleList.data.roles.map((role) => (
                        <MenuItem key={role.id} value={role.id}>
                          {role.roleDisplayName}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem disabled>No roles available</MenuItem>
                    )}
                  </TextField>
                </Grid>
                <Grid size={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={watch("accountStatus") === "Active"}
                        onChange={(e) => {
                          const newStatus = e.target.checked
                            ? "Active"
                            : "Inactive";
                          setValue("accountStatus", newStatus, {
                            shouldValidate: true,
                            shouldDirty: true,
                          });
                        }}
                      />
                    }
                    label={`Account Status: ${watch("accountStatus") ?? "Inactive"}`}
                  />
                </Grid>

                <Grid size={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={changePassword}
                        onChange={() => setChangePassword(!changePassword)}
                      />
                    }
                    label="Change Password"
                  />
                </Grid>
                {changePassword && (
                  <>
                    <Grid size={12}>
                      <TextField
                        {...register("password")}
                        fullWidth
                        label="New Password"
                        type={showPassword.value ? "text" : "password"}
                        error={!!errors.password}
                        helperText={errors.password?.message}
                        slotProps={{
                          inputLabel: { shrink: true },
                          input: {
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton
                                  onClick={showPassword.onToggle}
                                  edge="end"
                                >
                                  <Iconify
                                    icon={
                                      showPassword.value
                                        ? "solar:eye-bold"
                                        : "solar:eye-closed-bold"
                                    }
                                  />
                                </IconButton>
                              </InputAdornment>
                            ),
                          },
                        }}
                      />
                    </Grid>
                    <Grid size={12}>
                      <TextField
                        {...register("confirmPassword")}
                        fullWidth
                        label="Confirm Password"
                        type={showConfirmPassword.value ? "text" : "password"}
                        error={!!errors.confirmPassword}
                        helperText={errors.confirmPassword?.message}
                        slotProps={{
                          inputLabel: { shrink: true },
                          input: {
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton
                                  onClick={showConfirmPassword.onToggle}
                                  edge="end"
                                >
                                  <Iconify
                                    icon={
                                      showConfirmPassword.value
                                        ? "solar:eye-bold"
                                        : "solar:eye-closed-bold"
                                    }
                                  />
                                </IconButton>
                              </InputAdornment>
                            ),
                          },
                        }}
                      />
                    </Grid>
                  </>
                )}
                <Grid size={12} display="flex" justifyContent="flex-end">
                  <Button
                    type="submit"
                    variant="contained"
                    disabled={isLoading}
                    color="primary"
                    size="large"
                    startIcon={
                      isLoading ? <CircularProgress size={20} /> : null
                    }
                  >
                    Update User
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </Form>
        )}
      </Card>

      <Snackbar
        open={successMessage || !!errorMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={errorMessage ? "error" : "success"}
          variant="filled"
        >
          {errorMessage || "User updated successfully."}
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
}
