import { useState, useEffect } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableHead,
  TableBody,
  TableCell,
  TableRow,
  TableContainer,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useTheme } from "@mui/material/styles";
import { DashboardContent } from "../../layout/dashboard/content";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { paths } from "../../routes/paths";
import { AppWidgetSummary } from "../../components/app-widget-sumary/app-widget-summary";
import { AppCurrentDownload } from "../../components/app-current-download/app-current-download";
import LaptopMacIcon from "@mui/icons-material/LaptopMac";
import AndroidIcon from "@mui/icons-material/Android";
import DesktopWindowsIcon from "@mui/icons-material/DesktopWindows";
import AppleIcon from "@mui/icons-material/Apple";
import Chart from "react-apexcharts";
import { useGetLiveMetricsMutation } from "../../services/analyticsApi";

export default function LiveMetrics() {
  const [filter, setFilter] = useState("24h");
  const theme = useTheme();

  const timeRanges = [
    { label: "Last 1 Hour", value: "1h" },
    { label: "Last 24 Hours", value: "24h" },
    { label: "Last 7 Days", value: "7d" },
    { label: "Last 30 Days", value: "30d" },
    { label: "Last 90 Days", value: "90d" },
    { label: "Last 365 Days", value: "365d" },
  ];

  const [metrics, setMetrics] = useState();

  const osIcons = {
    Mac: <LaptopMacIcon color="primary" />,
    Windows: <DesktopWindowsIcon color="secondary" />,
    iOS: <AppleIcon color="action" />,
    Android: <AndroidIcon color="success" />,
  };

  const countryFlags = {
    USA: "https://flagcdn.com/w40/us.png",
    India: "https://flagcdn.com/w40/in.png",
    UK: "https://flagcdn.com/w40/gb.png",
    Canada: "https://flagcdn.com/w40/ca.png",
    Germany: "https://flagcdn.com/w40/de.png",
  };

  const trendMetricsRows = metrics?.trendMetrics
    ? [
        {
          name: "Today - Active Users",
          value: metrics.trendMetrics.today.activeUsers,
        },
        {
          name: "Today - New Users",
          value: metrics.trendMetrics.today.newUsers,
        },
      ]
    : [];

  const [getLiveMetrics, { isLoading, error }] = useGetLiveMetricsMutation();

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await getLiveMetrics({ range: filter }).unwrap();
        setMetrics(response.data);
      } catch (err) {
        console.error("Failed to fetch live metrics:", err);
      }
    };

    fetchMetrics();
  }, [filter]);

  return (
    <DashboardContent maxWidth="xl">
      <CustomBreadcrumbs
        heading="Live Metrics"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Live Metrics" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Box display="flex" justifyContent="flex-end" mb={2}>
        <FormControl size="small" sx={{ minWidth: 200 }}>
          <InputLabel>Filter by Time</InputLabel>
          <Select
            value={filter}
            label="Filter by Time"
            onChange={(e) => setFilter(e.target.value)}
          >
            {timeRanges.map((range) => (
              <MenuItem key={range.value} value={range.value}>
                {range.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      <Grid container spacing={3} alignItems="stretch">
        <Grid size={{ xs: 12, md: 4 }}>
          <Card sx={{ height: "100%" }}>
            <AppWidgetSummary
              title="Total Feeds"
              total={metrics?.feeds?.count || 0}
              percent={metrics?.feeds?.percentageChange}
              chart={{
                categories: [
                  "Jan",
                  "Feb",
                  "Mar",
                  "Apr",
                  "May",
                  "Jun",
                  "Jul",
                  "Aug",
                ],
                colors: [theme.palette.info.main],
                series: [50, 30, 20],
              }}
            />
          </Card>
        </Grid>

        <Grid size={{ xs: 12, md: 4 }}>
          <Card sx={{ height: "100%" }}>
            <AppWidgetSummary
              title="Total Users"
              total={metrics?.users?.count || 0}
              percent={metrics?.users?.percentageChange}
              chart={{
                colors: [theme.palette.primary.main],
                categories: [
                  "Jan",
                  "Feb",
                  "Mar",
                  "Apr",
                  "May",
                  "Jun",
                  "Jul",
                  "Aug",
                ],
                series: [40, 40, 20],
              }}
            />
          </Card>
        </Grid>

        <Grid size={{ xs: 12, md: 4 }}>
          <Card sx={{ height: "100%" }}>
            <AppWidgetSummary
              title="Active Users"
              total={metrics?.activeUsers?.count || 0}
              percent={metrics?.activeUsers?.percentageChange}
              chart={{
                colors: [theme.palette.success.main],
                categories: [
                  "Jan",
                  "Feb",
                  "Mar",
                  "Apr",
                  "May",
                  "Jun",
                  "Jul",
                  "Aug",
                ],
                series: [60, 25, 15],
              }}
            />
          </Card>
        </Grid>

        <Grid size={12}>
          <AppCurrentDownload
            title="Current Download"
            subheader="Downloaded by Operating System"
            chart={{
              series:
                Array.isArray(metrics?.downloads) &&
                metrics.downloads.length > 0
                  ? metrics.downloads
                  : [
                      { label: "Mac", value: 0 },
                      { label: "Windows", value: 0 },
                      { label: "iOS", value: 0 },
                      { label: "Android", value: 0 },
                    ],
            }}
          />
        </Grid>

        <Grid size={12}>
          <Card sx={{ height: "100%", p: 3 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Geography-wise Split
            </Typography>
            <List>
              {metrics?.geographySplit?.map((geo, index) => (
                <ListItem
                  key={index}
                  sx={{
                    borderBottom: "1px solid #ddd",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Box display="flex" alignItems="center" gap={2}>
                    <ListItemAvatar>
                      <Avatar
                        src={countryFlags[geo?.location]}
                        alt={geo?.location}
                      />
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Typography fontWeight="bold">
                          {geo?.location}
                        </Typography>
                      }
                    />
                  </Box>

                  <Box display="flex" gap={2}>
                    {geo?.devices?.map((device, i) => (
                      <Box key={i} display="flex" alignItems="center" gap={1}>
                        {osIcons[device?.os]}
                        <Typography variant="body2">{device?.count}</Typography>
                      </Box>
                    ))}
                  </Box>
                </ListItem>
              ))}
            </List>
          </Card>
        </Grid>

        <Grid size={12}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Timeline / Event Tracker
            </Typography>
            <List>
              {metrics?.eventList?.length ? (
                metrics?.eventList.map((event, idx) => (
                  <ListItem key={idx}>
                    <ListItemText
                      primary={event.name}
                      secondary={`At: ${event.timestamp}`}
                    />
                  </ListItem>
                ))
              ) : (
                <Typography>No events recorded.</Typography>
              )}
            </List>
          </Card>
        </Grid>

        <Grid size={12}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Engagement Trends: Posts & Live
            </Typography>
            {metrics?.engagementTrends?.length ? (
              <Chart
                options={{
                  chart: { id: "engagement-trends" },
                  xaxis: {
                    categories: metrics?.engagementTrends.map((t) => t.label),
                  },
                }}
                series={[
                  {
                    name: "Engagement",
                    data: metrics?.engagementTrends.map((t) => t.value),
                  },
                ]}
                type="line"
                height={300}
              />
            ) : (
              <Typography>No engagement trends available.</Typography>
            )}
          </Card>
        </Grid>

        <Grid size={12}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Trend Metrics
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Metric</TableCell>
                    <TableCell align="right">Value</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {trendMetricsRows.length ? (
                    trendMetricsRows.map((metric, idx) => (
                      <TableRow key={idx}>
                        <TableCell>{metric.name}</TableCell>
                        <TableCell align="right">{metric.value}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={2}>
                        No trend metrics available.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Card>
        </Grid>
      </Grid>

      {isLoading && <Typography>Loading live metrics...</Typography>}
      {error && <Typography color="error">Failed to load metrics.</Typography>}
    </DashboardContent>
  );
}
