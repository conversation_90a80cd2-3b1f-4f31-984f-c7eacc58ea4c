import {
  useGetSettingsMutation,
  useEditSettingsMutation,
} from "../../services/settingsApi"; // Make sure the path is correct
import {
  Card,
  Typography,
  Box,
  TextField,
  FormControlLabel,
  Switch,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useEffect, useState } from "react";

const SettingsManagement = () => {
  const [getSettings, { isLoading, isError }] = useGetSettingsMutation();
  const [editSettings] = useEditSettingsMutation();

  const [config, setConfig] = useState({
    theme: "light",
    language: "en",
    privacyPolicy: "",
    experimentalFeatures: false,
    apiKey: "",
    integrationHealth: "Healthy",
    autoSync: false,
    errorLogs: "",
  });

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await getSettings().unwrap();
        setConfig((prev) => ({
          ...prev,
          ...response,
          // Ensure all required fields are defined
          experimentalFeatures: response.experimentalFeatures ?? false,
          autoSync: response.autoSync ?? false,
        }));
      } catch (error) {
        console.error("Failed to load settings:", error);
      }
    };

    fetchSettings();
  }, [getSettings]);

  const handleChange = (field, value) => {
    setConfig((prev) => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    try {
      const response = await editSettings(config).unwrap();
      console.log("Updated Settings:", response);
    } catch (error) {
      console.error("Failed to save settings:", error);
    }
  };

  if (isLoading) return <CircularProgress />;
  if (isError)
    return <Typography color="error">Failed to load settings.</Typography>;

  return (
    <Grid container spacing={6}>
      {/* Platform Configuration */}
      <Grid xs={7} md={7}>
        <Card sx={{ p: 3, ml: 4 }}>
          <Typography variant="h6" mb={2}>
            Platform Configuration
          </Typography>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <FormControl fullWidth>
              <InputLabel>Theme</InputLabel>
              <Select
                value={config.theme}
                label="Theme"
                onChange={(e) => handleChange("theme", e.target.value)}
              >
                <MenuItem value="light">Light</MenuItem>
                <MenuItem value="dark">Dark</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Language</InputLabel>
              <Select
                value={config.language}
                label="Language"
                onChange={(e) => handleChange("language", e.target.value)}
              >
                <MenuItem value="en">English</MenuItem>
                <MenuItem value="es">Spanish</MenuItem>
                <MenuItem value="fr">French</MenuItem>
              </Select>
            </FormControl>

            <TextField
              label="Privacy Policy URL"
              value={config.privacyPolicy}
              onChange={(e) => handleChange("privacyPolicy", e.target.value)}
              fullWidth
            />

            <FormControlLabel
              control={
                <Switch
                  checked={config.experimentalFeatures}
                  onChange={(e) =>
                    handleChange("experimentalFeatures", e.target.checked)
                  }
                />
              }
              label="Enable Experimental Features"
            />

            <TextField
              label="API Key"
              value={config.apiKey}
              onChange={(e) => handleChange("apiKey", e.target.value)}
              fullWidth
            />

            <Button variant="contained" color="primary" onClick={handleSave}>
              Save Changes
            </Button>
          </Box>
        </Card>
      </Grid>

      {/* Manage Integrations */}
      <Grid xs={7} md={7}>
        <Card sx={{ p: 3 }}>
          <Typography variant="h6" mb={2}>
            Manage Integrations
          </Typography>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <TextField
              label="Integration Health Status"
              value={config.integrationHealth}
              onChange={(e) =>
                handleChange("integrationHealth", e.target.value)
              }
              fullWidth
              disabled
            />

            <FormControlLabel
              control={
                <Switch
                  checked={config.autoSync}
                  onChange={(e) => handleChange("autoSync", e.target.checked)}
                />
              }
              label="Enable Auto-Sync with External Platforms"
            />

            <TextField
              label="Error Logs"
              value={config.errorLogs}
              onChange={(e) => handleChange("errorLogs", e.target.value)}
              multiline
              rows={4}
              fullWidth
              disabled
            />
          </Box>
        </Card>
      </Grid>
    </Grid>
  );
};

export default SettingsManagement;
