import { useState, useEffect, useCallback } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Box,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Paper,
  Chip,
  Snackbar,
  Alert,
  Avatar,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import { paths } from "../../routes/paths";
import InputFileUpload from "../../components/file-upload";
import {
  useGetSettingsMutation,
  useEditSettingsMutation,
} from "../../services/settingsApi";
import {
  useGetRoleListMutation,
  useGetPermissionListMutation,
  useGetRolePermissionsMutation,
  useUpdateRolePermissionsMutation,
} from "../../services/roleApi";
import { uploadToS3 } from "../../utils/file-upload";
import { useSelector } from "react-redux";

const SettingsManagement = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  const accessToken = useSelector((state) => state.auth.accessToken);

  // General Settings State
  const [generalSettings, setGeneralSettings] = useState({
    platformName: "",
    logo: null,
    logoUrl: "",
    timeZone: "",
    defaultLanguage: "en",
  });

  // RBAC State
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [rolePermissions, setRolePermissions] = useState({});
  const [rbacLoading, setRbacLoading] = useState(false);

  // API Hooks
  const [getSettings] = useGetSettingsMutation();
  const [editSettings] = useEditSettingsMutation();
  const [getRoleList] = useGetRoleListMutation();
  const [getPermissionList] = useGetPermissionListMutation();
  const [getRolePermissions] = useGetRolePermissionsMutation();
  const [updateRolePermissions] = useUpdateRolePermissionsMutation();

  // Load initial data
  useEffect(() => {
    loadGeneralSettings();
    loadRBACData();
  }, []);

  const loadGeneralSettings = async () => {
    try {
      const response = await getSettings().unwrap();
      setGeneralSettings(prev => ({
        ...prev,
        ...response.data,
      }));
    } catch (error) {
      console.error("Failed to load general settings:", error);
    }
  };

  const loadRBACData = async () => {
    try {
      setRbacLoading(true);

      const [rolesResponse, permissionsResponse, rolePermissionsResponse] = await Promise.all([
        getRoleList().unwrap(),
        getPermissionList().unwrap(),
        getRolePermissions().unwrap(),
      ]);

      setRoles(rolesResponse.data?.roles || []);
      setPermissions(permissionsResponse.data?.permissions || []);

      // Convert role permissions to matrix format
      const permissionMatrix = {};
      if (rolePermissionsResponse.data?.rolePermissions) {
        rolePermissionsResponse.data.rolePermissions.forEach((rp) => {
          if (!permissionMatrix[rp.roleId]) {
            permissionMatrix[rp.roleId] = {};
          }
          permissionMatrix[rp.roleId][rp.permissionId] = true;
        });
      }
      setRolePermissions(permissionMatrix);

    } catch (error) {
      console.error("Error loading RBAC data:", error);
      setErrorMessage("Failed to load role permissions data");
    } finally {
      setRbacLoading(false);
    }
  };

  // Handler functions
  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleGeneralSettingsChange = (field, value) => {
    setGeneralSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleLogoUpload = async (file) => {
    if (!file) return;

    try {
      setLoading(true);
      const logoUrl = await uploadToS3(file, accessToken, "/media/generate-presigned-url");
      setGeneralSettings(prev => ({
        ...prev,
        logo: file,
        logoUrl: logoUrl
      }));
    } catch (error) {
      setErrorMessage("Failed to upload logo");
    } finally {
      setLoading(false);
    }
  };

  const handleSaveGeneralSettings = async () => {
    try {
      setLoading(true);
      await editSettings(generalSettings).unwrap();
      setSuccessMessage("General settings saved successfully!");
    } catch (error) {
      setErrorMessage("Failed to save general settings");
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionToggle = useCallback((roleId, permissionId) => {
    setRolePermissions(prev => ({
      ...prev,
      [roleId]: {
        ...prev[roleId],
        [permissionId]: !prev[roleId]?.[permissionId]
      }
    }));
  }, []);

  const handleSaveRolePermissions = async () => {
    try {
      setLoading(true);

      const rolePermissionUpdates = [];
      Object.keys(rolePermissions).forEach(roleId => {
        Object.keys(rolePermissions[roleId]).forEach(permissionId => {
          if (rolePermissions[roleId][permissionId]) {
            rolePermissionUpdates.push({
              roleId: parseInt(roleId),
              permissionId: parseInt(permissionId)
            });
          }
        });
      });

      await updateRolePermissions({ rolePermissions: rolePermissionUpdates }).unwrap();
      setSuccessMessage("Role permissions updated successfully!");

    } catch (error) {
      setErrorMessage("Failed to update role permissions");
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSuccessMessage("");
    setErrorMessage("");
  };

  const isPermissionGranted = (roleId, permissionId) => {
    return rolePermissions[roleId]?.[permissionId] || false;
  };

  const getPermissionCount = (roleId) => {
    return Object.values(rolePermissions[roleId] || {}).filter(Boolean).length;
  };

  // Time zones list
  const timeZones = [
    { value: "UTC", label: "UTC" },
    { value: "America/New_York", label: "Eastern Time (ET)" },
    { value: "America/Chicago", label: "Central Time (CT)" },
    { value: "America/Denver", label: "Mountain Time (MT)" },
    { value: "America/Los_Angeles", label: "Pacific Time (PT)" },
    { value: "Europe/London", label: "London (GMT)" },
    { value: "Europe/Paris", label: "Paris (CET)" },
    { value: "Asia/Tokyo", label: "Tokyo (JST)" },
    { value: "Asia/Shanghai", label: "Shanghai (CST)" },
    { value: "Asia/Kolkata", label: "India (IST)" },
  ];

  // Languages list
  const languages = [
    { value: "en", label: "English" },
    { value: "es", label: "Spanish" },
    { value: "fr", label: "French" },
    { value: "de", label: "German" },
    { value: "it", label: "Italian" },
    { value: "pt", label: "Portuguese" },
    { value: "zh", label: "Chinese" },
    { value: "ja", label: "Japanese" },
    { value: "ko", label: "Korean" },
    { value: "ar", label: "Arabic" },
  ];

  // Render General Settings Tab
  const renderGeneralSettings = () => (
    <Card sx={{ p: 4 }}>
      <Typography variant="h6" gutterBottom>
        General Settings
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Configure platform-wide settings and preferences.
      </Typography>

      <Grid container spacing={3}>
        <Grid size={12}>
          <TextField
            fullWidth
            label="Platform Name"
            value={generalSettings.platformName}
            onChange={(e) => handleGeneralSettingsChange("platformName", e.target.value)}
            placeholder="Enter your platform name"
          />
        </Grid>

        <Grid size={12}>
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Platform Logo
            </Typography>
            {generalSettings.logoUrl && (
              <Box sx={{ mb: 2 }}>
                <Avatar
                  src={generalSettings.logoUrl}
                  alt="Platform Logo"
                  sx={{ width: 80, height: 80, mb: 1 }}
                  variant="rounded"
                />
                <Typography variant="caption" color="text.secondary">
                  Current Logo
                </Typography>
              </Box>
            )}
            <InputFileUpload
              name="logo"
              label="Upload New Logo"
              value={generalSettings.logo}
              onChange={(e) => handleLogoUpload(e.target.files?.[0])}
              accept="image/*"
            />
          </Box>
        </Grid>

        <Grid size={6}>
          <FormControl fullWidth>
            <InputLabel>Time Zone</InputLabel>
            <Select
              value={generalSettings.timeZone}
              label="Time Zone"
              onChange={(e) => handleGeneralSettingsChange("timeZone", e.target.value)}
            >
              {timeZones.map((tz) => (
                <MenuItem key={tz.value} value={tz.value}>
                  {tz.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid size={6}>
          <FormControl fullWidth>
            <InputLabel>Default Language</InputLabel>
            <Select
              value={generalSettings.defaultLanguage}
              label="Default Language"
              onChange={(e) => handleGeneralSettingsChange("defaultLanguage", e.target.value)}
            >
              {languages.map((lang) => (
                <MenuItem key={lang.value} value={lang.value}>
                  {lang.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid size={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              onClick={handleSaveGeneralSettings}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : null}
            >
              Save General Settings
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Card>
  );

  // Render RBAC Matrix Tab
  const renderRBACMatrix = () => {
    if (rbacLoading) {
      return (
        <Card sx={{ p: 4 }}>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Card>
      );
    }

    return (
      <Card sx={{ overflow: "hidden" }}>
        <Box sx={{ p: 3, borderBottom: 1, borderColor: "divider" }}>
          <Typography variant="h6" gutterBottom>
            Role Permission Matrix (RBAC)
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage permissions for each role. Check the boxes to grant permissions.
          </Typography>
          <Box sx={{ mt: 2 }}>
            <Button
              variant="contained"
              onClick={handleSaveRolePermissions}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : null}
            >
              Save Permission Changes
            </Button>
          </Box>
        </Box>

        <TableContainer component={Paper} sx={{ maxHeight: "60vh" }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{
                    minWidth: 200,
                    fontWeight: "bold",
                    backgroundColor: "grey.50",
                    position: "sticky",
                    left: 0,
                    zIndex: 1
                  }}
                >
                  Permissions
                </TableCell>
                {roles.map((role) => (
                  <TableCell
                    key={role.id}
                    align="center"
                    sx={{
                      minWidth: 120,
                      fontWeight: "bold",
                      backgroundColor: "grey.50"
                    }}
                  >
                    <Box>
                      <Typography variant="subtitle2" noWrap>
                        {role.roleDisplayName}
                      </Typography>
                      <Chip
                        label={`${getPermissionCount(role.id)} permissions`}
                        size="small"
                        color="primary"
                        variant="outlined"
                        sx={{ mt: 0.5 }}
                      />
                    </Box>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {permissions.map((permission) => (
                <TableRow key={permission.id} hover>
                  <TableCell
                    sx={{
                      fontWeight: "medium",
                      backgroundColor: "background.paper",
                      position: "sticky",
                      left: 0,
                      zIndex: 1,
                      borderRight: 1,
                      borderColor: "divider"
                    }}
                  >
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {permission.name}
                      </Typography>
                      {permission.description && (
                        <Typography variant="caption" color="text.secondary">
                          {permission.description}
                        </Typography>
                      )}
                    </Box>
                  </TableCell>
                  {roles.map((role) => (
                    <TableCell key={`${role.id}-${permission.id}`} align="center">
                      <Checkbox
                        checked={isPermissionGranted(role.id, permission.id)}
                        onChange={() => handlePermissionToggle(role.id, permission.id)}
                        color="primary"
                      />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>
    );
  };

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Settings"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Settings" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card sx={{ mb: 3 }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          sx={{ borderBottom: 1, borderColor: "divider" }}
        >
          <Tab label="General Settings" />
          <Tab label="Role Permissions (RBAC)" />
        </Tabs>
      </Card>

      <Box sx={{ mt: 3 }}>
        {currentTab === 0 && renderGeneralSettings()}
        {currentTab === 1 && renderRBACMatrix()}
      </Box>

      <Snackbar
        open={!!successMessage || !!errorMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={errorMessage ? "error" : "success"}
          variant="filled"
        >
          {successMessage || errorMessage}
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
};

export default SettingsManagement;
