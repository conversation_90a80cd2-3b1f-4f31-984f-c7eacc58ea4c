import { z as zod } from "zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useBoolean } from "minimal-shared/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Box,
  Link,
  Alert,
  IconButton,
  InputAdornment,
  TextField,
  Button,
  CircularProgress,
} from "@mui/material";
import { paths } from "../../routes/paths";
import { useRouter } from "../../components/routes/hooks";
import { RouterLink } from "../../components/routes/components";
import { Iconify } from "../../components/iconify";
import { Form } from "../../components/hook-form/form-provider";
import { getErrorMessage } from "../../utils/error-message";
import { FormHead } from "../../components/auth/form-head";
import { useLoginMutation } from "../../services/authApi";
import { useDispatch } from "react-redux";
import { setCredentials } from "../../services/authSlice";
import useAuth from "../../utils/useAuth";

export const SignInSchema = zod.object({
  email: zod
    .string()
    .min(1, { message: "Email is required!" })
    .email({ message: "Email must be a valid email address!" }),
  password: zod
    .string()
    .min(1, { message: "Password is required!" })
    .min(6, { message: "Password must be at least 6 characters!" }),
});

export default function SignInPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const showPassword = useBoolean();
  const { isAuthenticated, loading } = useAuth();
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    if (!loading && isAuthenticated) {
      router.push("/");
    }
  }, [isAuthenticated, loading, router]);

  const [login, { isLoading }] = useLoginMutation();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(SignInSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data) => {
    try {
      const response = await login(data).unwrap();
      dispatch(setCredentials(response.data));
      router.push("/");
    } catch (error) {
      setErrorMessage(getErrorMessage(error.data.message));
    }
  };

  if (loading || isAuthenticated) return null;

  return (
    <>
      <Box
        sx={{
          mx: "auto",
          mb: 5,
          width: 200,
          height: 100,
        }}
        component="img"
        alt="Logo"
        src={"/pipaan.svg"}
      />
      <FormHead
        title="Sign in to your account"
        sx={{ textAlign: { xs: "center", md: "left" } }}
      />

      {!!errorMessage && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {errorMessage}
        </Alert>
      )}

      <Form
        methods={{ handleSubmit, register }}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Box sx={{ gap: 3, display: "flex", flexDirection: "column" }}>
          <TextField
            {...register("email")}
            label="Email"
            placeholder="Email address"
            error={!!errors.email}
            helperText={errors.email?.message}
            slotProps={{ inputLabel: { shrink: true } }}
          />

          <Box sx={{ gap: 1.5, display: "flex", flexDirection: "column" }}>
            <Link
              component={RouterLink}
              href={paths.auth.forgotPassword}
              variant="body2"
              color="inherit"
              sx={{ alignSelf: "flex-end" }}
            >
              Forgot password?
            </Link>

            <TextField
              {...register("password")}
              label="Password"
              placeholder="password"
              type={showPassword.value ? "text" : "password"}
              error={!!errors.password}
              helperText={errors.password?.message}
              slotProps={{
                inputLabel: { shrink: true },
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton onClick={showPassword.onToggle} edge="end">
                        <Iconify
                          icon={
                            showPassword.value
                              ? "solar:eye-bold"
                              : "solar:eye-closed-bold"
                          }
                        />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
            />
          </Box>

          <Button
            fullWidth
            color="inherit"
            size="large"
            type="submit"
            variant="contained"
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {isLoading ? "Signing in..." : "Sign in"}
          </Button>
        </Box>
      </Form>
    </>
  );
}
