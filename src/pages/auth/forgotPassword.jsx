import { useForm } from "react-hook-form";
import { z as zod } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormHead } from "../../components/auth/form-head";
import { Form } from "../../components/hook-form/form-provider";
import { Box, Alert, Button, CircularProgress, TextField } from "@mui/material";
import { FormReturnLink } from "../../components/auth/form-return-link";
import { paths } from "../../routes/paths";
import { getErrorMessage } from "../../utils/error-message";
import { useForgotPasswordMutation } from "../../services/authApi";
import { useState } from "react";

export const ResetPasswordSchema = zod.object({
  email: zod
    .string()
    .min(1, { message: "Email is required!" })
    .email({ message: "Email must be a valid email address!" }),
});

export default function ForgotPasswordPage() {
  const [errorMessage, setErrorMessage] = useState("");
  const [forgotPassword, { isLoading, isSuccess }] =
    useForgotPasswordMutation();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(ResetPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (data) => {
    try {
      await forgotPassword({ email: data.email }).unwrap();
    } catch (error) {
      setErrorMessage(getErrorMessage(error?.data?.message));
    }
  };

  return (
    <>
      <Box
        sx={{
          mx: "auto",
          mb: 5,
          width: 200,
          height: 100,
        }}
        component="img"
        alt="Logo"
        src={"/pipaan.svg"}
      />
      <FormHead
        title="Forgot your password?"
        description={`Please enter the email address associated with your account and we'll email you a link to reset your password.`}
      />

      {!!errorMessage && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {errorMessage}
        </Alert>
      )}

      {isSuccess && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Check your email for the reset link!
        </Alert>
      )}

      <Form
        methods={{ handleSubmit, register }}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Box sx={{ gap: 3, display: "flex", flexDirection: "column" }}>
          <TextField
            {...register("email")}
            label="Email"
            placeholder="Enter your email"
            error={!!errors.email}
            helperText={errors.email?.message}
            slotProps={{ inputLabel: { shrink: true } }}
          />

          <Button
            fullWidth
            color="inherit"
            size="large"
            type="submit"
            variant="contained"
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {isLoading ? "Sending request..." : "Submit"}
          </Button>

          <FormReturnLink href={paths.auth.signIn} />
        </Box>
      </Form>
    </>
  );
}
