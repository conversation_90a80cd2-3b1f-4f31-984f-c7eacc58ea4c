import { useState } from "react";
import { useForm } from "react-hook-form";
import { z as zod } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSearchParams } from "react-router-dom";
import {
  Box,
  Button,
  TextField,
  CircularProgress,
  Alert,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Form } from "../../components/hook-form/form-provider";
import { FormHead } from "../../components/auth/form-head";
import { useResetPasswordMutation } from "../../services/authApi";
import { paths } from "../../routes/paths";
import { FormReturnLink } from "../../components/auth/form-return-link";
import { useBoolean } from "minimal-shared/hooks";
import { Iconify } from "../../components/iconify";

const ResetPasswordSchema = zod
  .object({
    password: zod
      .string()
      .min(8, { message: "Password must be at least 8 characters!" })
      .max(20, { message: "Password cannot exceed 20 characters!" })
      .regex(/[A-Z]/, {
        message: "Password must contain at least one uppercase letter!",
      })
      .regex(/\d/, { message: "Password must contain at least one number!" })
      .regex(/[@$!%*?&]/, {
        message: "Password must contain at least one special character!",
      }),
    confirmPassword: zod.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords must match!",
    path: ["confirmPassword"],
  });

export default function ResetPasswordPage() {
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");
  const showPassword = useBoolean();
  const showConfirmPassword = useBoolean();
  const [resetPassword, { isLoading, isSuccess }] = useResetPasswordMutation();
  const [errorMessage, setErrorMessage] = useState("");

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(ResetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (data) => {
    try {
      await resetPassword({ token, password: data.password }).unwrap();
      setErrorMessage("");
    } catch (error) {
      setErrorMessage(error.data?.message || "Failed to reset password.");
    }
  };

  return (
    <>
      <Box
        sx={{
          mx: "auto",
          mb: 5,
          width: 200,
          height: 100,
        }}
        component="img"
        alt="Logo"
        src={"/pipaan.svg"}
      />
      <FormHead
        title="Reset Your Password"
        description="Enter a new password to reset your account."
      />

      {!!errorMessage && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {errorMessage}
        </Alert>
      )}

      {isSuccess && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Password reset successfully! You can now log in
        </Alert>
      )}

      <Form
        methods={{ handleSubmit, register }}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Box sx={{ gap: 3, display: "flex", flexDirection: "column" }}>
          <TextField
            {...register("password")}
            label="New Password"
            type={showPassword.value ? "text" : "password"}
            error={!!errors.password}
            helperText={errors.password?.message}
            slotProps={{
              inputLabel: { shrink: true },
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton onClick={showPassword.onToggle} edge="end">
                      <Iconify
                        icon={
                          showPassword.value
                            ? "solar:eye-bold"
                            : "solar:eye-closed-bold"
                        }
                      />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <TextField
            {...register("confirmPassword")}
            label="Confirm Password"
            type={showConfirmPassword.value ? "text" : "password"}
            error={!!errors.confirmPassword}
            helperText={errors.confirmPassword?.message}
            slotProps={{
              inputLabel: { shrink: true },
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={showConfirmPassword.onToggle}
                      edge="end"
                    >
                      <Iconify
                        icon={
                          showConfirmPassword.value
                            ? "solar:eye-bold"
                            : "solar:eye-closed-bold"
                        }
                      />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />
          <Button
            fullWidth
            color="inherit"
            size="large"
            type="submit"
            variant="contained"
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {isLoading ? "Resetting..." : "Reset Password"}
          </Button>

          <FormReturnLink href={paths.auth.signIn} />
        </Box>
      </Form>
    </>
  );
}
