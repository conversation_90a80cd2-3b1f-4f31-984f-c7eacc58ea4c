import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z as zod } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useParams } from "react-router";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import {
  Card,
  TextField,
  Button,
  Box,
  Snackbar,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import {
  useEditCustomerMutation,
  useGetCustomerDetailMutation,
} from "../../services/customerApi";
import { useRouter } from "../../components/routes/hooks";

const EditCustomerSchema = zod.object({
  first_name: zod.string().min(1, "First name is required."),
  last_name: zod.string().min(1, "Last name is required."),
  email: zod.string().email("Invalid email address."),
  mobile_number: zod.string().min(10, "Invalid phone number."),
  role: zod.number().min(1, "Role is required."),
  accountStatus: zod.enum(["Active", "Inactive"], {
    message: "Invalid status selected.",
  }),
});

export default function CustomerEdit() {
  const { id } = useParams();
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState(false);

  const [editCustomer, { isLoading }] = useEditCustomerMutation();
  const [
    getCustomerDetail,
    { data: customerData, isLoading: isCustomerDataLoading },
  ] = useGetCustomerDetailMutation();

  useEffect(() => {
    getCustomerDetail({ userId: id });
  }, [getCustomerDetail, id]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch,
  } = useForm({
    resolver: zodResolver(EditCustomerSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      mobile_number: "",
      role: "",
      accountStatus: "Active",
    },
  });

  useEffect(() => {
    if (customerData?.data) {
      reset({
        first_name: customerData.data.first_name,
        last_name: customerData.data.last_name,
        email: customerData.data.email,
        mobile_number: customerData.data.mobile_number,
        role: customerData.data.role,
        accountStatus: customerData.data.status,
      });
    }
  }, [customerData, reset]);

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const onSubmit = async (data) => {
    try {
      await editCustomer({ userId: id, ...data }).unwrap();
      setSuccessMessage(true);
      setTimeout(() => {
        router.push("/customer");
      }, 1000);
    } catch (error) {
      console.error("Update Error:", error);
      setErrorMessage(error?.data?.message || "Failed to update Customer.");
    }
  };

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Edit Customer"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Customer", href: paths.main.customer.list },
          { name: "Edit" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: isCustomerDataLoading ? "center" : "flex-start",
        }}
      >
        {isCustomerDataLoading ? (
          <CircularProgress />
        ) : (
          <Form
            methods={{ handleSubmit, register }}
            onSubmit={handleSubmit(onSubmit)}
          >
            <Box mt={3}>
              <Grid container spacing={2}>
                <Grid size={12}>
                  <TextField
                    {...register("first_name")}
                    fullWidth
                    label="First Name"
                    error={!!errors.first_name}
                    helperText={errors.first_name?.message}
                    value={watch("first_name") || ""}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    {...register("last_name")}
                    fullWidth
                    label="Last Name"
                    error={!!errors.last_name}
                    helperText={errors.last_name?.message}
                    value={watch("last_name") || ""}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    {...register("email")}
                    fullWidth
                    label="Email Address"
                    type="email"
                    error={!!errors.email}
                    helperText={errors.email?.message}
                    value={watch("email") || ""}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    {...register("mobile_number")}
                    fullWidth
                    label="Phone Number"
                    error={!!errors.mobile_number}
                    helperText={errors.mobile_number?.message}
                    value={watch("mobile_number") || ""}
                  />
                </Grid>
                <Grid size={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={watch("accountStatus") === "Active"}
                        onChange={(e) => {
                          const newStatus = e.target.checked
                            ? "Active"
                            : "Inactive";
                          setValue("accountStatus", newStatus, {
                            shouldValidate: true,
                            shouldDirty: true,
                          });
                        }}
                      />
                    }
                    label={`Account Status: ${watch("accountStatus") ?? "Inactive"}`}
                  />
                </Grid>
                <Grid size={12} display="flex" justifyContent="flex-end">
                  <Button
                    type="submit"
                    variant="contained"
                    disabled={isLoading}
                    color="primary"
                    size="large"
                    startIcon={
                      isLoading ? <CircularProgress size={20} /> : null
                    }
                  >
                    Update Customer
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </Form>
        )}
      </Card>

      <Snackbar
        open={successMessage || !!errorMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={errorMessage ? "error" : "success"}
          variant="filled"
        >
          {errorMessage || "Customer updated successfully."}
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
}
