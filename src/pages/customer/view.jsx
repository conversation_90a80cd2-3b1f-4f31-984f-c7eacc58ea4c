import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import { Card, TextField, Box, CircularProgress } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import { useGetCustomerDetailMutation } from "../../services/customerApi";
import { useParams } from "react-router";
import { fDate, fTime } from "../../utils/format-time";

export default function CustomerView() {
  const { id } = useParams();
  const [getCustomerDetail, { data: customerData, isLoading }] =
    useGetCustomerDetailMutation();

  const { watch, setValue } = useForm({});

  useEffect(() => {
    getCustomerDetail({ userId: id });
  }, [getCustomerDetail, id]);

  useEffect(() => {
    if (customerData?.data) {
      Object.entries(customerData.data).forEach(([key, value]) => {
        setValue(key, value);
      });
    }
  }, [customerData, setValue]);

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Customer Details"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Customers", href: paths.main.customer.list },
          { name: "Customer Details" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: isLoading ? "center" : "flex-start",
        }}
      >
        {isLoading ? (
          <CircularProgress />
        ) : (
          <Form>
            <Box mt={3}>
              <Grid container spacing={2}>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Customers ID"
                    value={watch("id") || "----"}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Full Name"
                    value={`${watch("first_name") || "----"} ${watch("last_name") || ""}`}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Email Address"
                    value={watch("email") || "----"}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Phone Number"
                    value={watch("mobile_number") || "----"}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    label="Date of Birth"
                    disabled
                    value={
                      watch("date_of_birth")
                        ? `${fDate(watch("date_of_birth"))}`
                        : "----"
                    }
                  ></TextField>
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    label="Address"
                    disabled
                    value={`${watch("location") || "----"} ${watch("country") || ""}`}
                  ></TextField>
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Account Status"
                    value={watch("status") || "----"}
                  ></TextField>
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Created date"
                    value={
                      watch("created_at")
                        ? `${fDate(watch("created_at"))} ${fTime(watch("created_at"))}`
                        : "----"
                    }
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Last Login"
                    value={
                      watch("last_login_at")
                        ? `${fDate(watch("last_login_at"))} ${fTime(watch("last_login_at"))}`
                        : "----"
                    }
                  />
                </Grid>
              </Grid>
            </Box>
          </Form>
        )}
      </Card>
    </DashboardContent>
  );
}
