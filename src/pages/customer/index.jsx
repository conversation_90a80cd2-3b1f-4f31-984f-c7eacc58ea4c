import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Snackbar } from "@mui/material";
import { GridActionsCellItem } from "@mui/x-data-grid";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { ConfirmDialog } from "../../components/custom-dialog";
import { Iconify } from "../../components/iconify";
import { DashboardContent } from "../../layout/dashboard/content";
import { paths } from "../../routes/paths";
import {
  useDeleteCustomerMutation,
  useGetCustomerListMutation,
} from "../../services/customerApi";
import {
  GridActionsLinkItem,
  RenderDateCell,
  RenderStatus,
  RenderText,
} from "../../components/table/common";
import { TableData } from "../../components/table/table-content";

export default function CustomerList() {
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [deleteId, setDeleteId] = useState("");
  const [tableData, setTableData] = useState([]);
  const [getCustomerList, { data: customers, isLoading: customersLoading }] =
    useGetCustomerListMutation();
  const [deleteCustomer] = useDeleteCustomerMutation();

  useEffect(() => {
    getCustomerList();
  }, []);

  useEffect(() => {
    if (customers?.data?.users?.length) {
      const processedCustomers = customers.data.users.map((user) => ({
        ...user,
        full_name: [user.first_name, user.last_name].filter(Boolean).join(" "),
      }));
      setTableData(processedCustomers);
    }
  }, [customers]);

  const handleDeleteRow = useCallback(async () => {
    try {
      await deleteCustomer({ userId: deleteId }).unwrap();
      setSuccessMessage("Customer deleted successfully!");
      setDeleteId("");
      getCustomerList();
    } catch (error) {
      setErrorMessage("Failed to delete customer.");
    }
  }, [deleteCustomer, deleteId, getCustomerList]);

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const columns = [
    {
      field: "id",
      headerName: "Customer ID",
      flex: 1,
      minWidth: 160,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.id} />,
    },
    {
      field: "full_name",
      headerName: "Full Name",
      flex: 1,
      minWidth: 160,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.full_name} />,
    },
    {
      field: "email",
      headerName: "Email",
      flex: 1,
      minWidth: 160,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.email} />,
    },
    {
      field: "mobile_number",
      headerName: "Phone",
      flex: 1,
      minWidth: 140,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.mobile_number} />,
    },
    {
      field: "created_at",
      headerName: "Registration Date",
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      renderCell: (params) => <RenderDateCell date={params.row.created_at} />,
    },
    {
      field: "last_login_at",
      headerName: "Last Activity Date",
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      renderCell: (params) => (
        <RenderDateCell date={params.row.last_login_at} />
      ),
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      minWidth: 110,
      disableColumnMenu: true,
      renderCell: (params) => <RenderStatus status={params.row.status} />,
    },
    {
      type: "actions",
      field: "actions",
      headerName: "Actions",
      flex: 1,
      minWidth: 80,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      getActions: (params) => [
        <GridActionsLinkItem
          showInMenu
          icon={<Iconify icon="solar:eye-bold" />}
          label="View"
          href={paths.main.customer.details(params.row.id)}
        />,
        <GridActionsLinkItem
          showInMenu
          icon={<Iconify icon="solar:pen-bold" />}
          label="Edit"
          href={paths.main.customer.edit(params.row.id)}
        />,
        <GridActionsCellItem
          showInMenu
          icon={<Iconify icon="solar:trash-bin-trash-bold" />}
          label="Delete"
          onClick={() => setDeleteId(params.row.id)}
          sx={{ color: "error.main" }}
        />,
      ],
    },
  ];

  const renderConfirmDialog = () => (
    <ConfirmDialog
      open={!!deleteId}
      onClose={() => setDeleteId("")}
      title="Delete"
      content={<>Are you sure want to delete?</>}
      action={
        <Button
          variant="contained"
          color="error"
          onClick={() => {
            handleDeleteRow();
          }}
        >
          Delete
        </Button>
      }
    />
  );

  return (
    <>
      <DashboardContent
        sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}
      >
        <CustomBreadcrumbs
          heading="Customers"
          links={[
            { name: "Dashboard", href: paths.main.root },
            { name: "Customers" },
          ]}
          sx={{ mb: { xs: 3, md: 5 } }}
        />

        <Card
          sx={{
            flexGrow: { md: 1 },
            display: { md: "flex" },
            height: { xs: 800, md: "1px" },
            flexDirection: { md: "column" },
          }}
        >
          <TableData
            tableData={tableData}
            columns={columns}
            productsLoading={customersLoading}
          />
        </Card>

        {/* Snackbar Notification */}
        <Snackbar
          open={!!successMessage || !!errorMessage}
          autoHideDuration={3000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "top", horizontal: "right" }} // Position of Snackbar
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={errorMessage ? "error" : "success"}
            variant="filled"
          >
            {successMessage || errorMessage}
          </Alert>
        </Snackbar>
      </DashboardContent>

      {renderConfirmDialog()}
    </>
  );
}
