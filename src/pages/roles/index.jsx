import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Snackbar } from "@mui/material";
import { GridActionsCellItem } from "@mui/x-data-grid";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { ConfirmDialog } from "../../components/custom-dialog";
import { Iconify } from "../../components/iconify";
import { DashboardContent } from "../../layout/dashboard/content";
import { paths } from "../../routes/paths";
import { RouterLink } from "../../components/routes/components";
import {
  useDeleteRoleMutation,
  useGetRoleListMutation,
} from "../../services/roleApi";
import {
  GridActionsLinkItem,
  RenderDateCell,
  RenderText,
} from "../../components/table/common";
import { TableData } from "../../components/table/table-content";

export default function RolesList() {
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [deleteId, setDeleteId] = useState("");
  const [tableData, setTableData] = useState([]);
  const [getRoleList, { data: roles, isLoading: rolesLoading }] =
    useGetRoleListMutation();
  const [deleteRole] = useDeleteRoleMutation();

  useEffect(() => {
    getRoleList();
  }, []);

  useEffect(() => {
    if (roles?.data?.roles?.length) {
      setTableData(roles.data.roles);
    }
  }, [roles]);

  const handleDeleteRow = useCallback(async () => {
    try {
      await deleteRole({ roleId: deleteId }).unwrap();
      setSuccessMessage("Role deleted successfully!");
      setDeleteId("");
      getRoleList();
    } catch (error) {
      setErrorMessage("Failed to delete role.");
    }
  }, [deleteRole, deleteId, getRoleList]);

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const columns = [
    {
      field: "id",
      headerName: "Role ID",
      flex: 1,
      minWidth: 100,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.id} />,
    },
    {
      field: "roleDisplayName",
      headerName: "Role Name",
      flex: 2,
      minWidth: 200,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.roleDisplayName} />,
    },
    {
      field: "description",
      headerName: "Description",
      flex: 3,
      minWidth: 250,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.description || "No description"} />,
    },
    {
      field: "created_at",
      headerName: "Created At",
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      renderCell: (params) => <RenderDateCell date={params.row.created_at} />,
    },
    {
      field: "updated_at",
      headerName: "Updated At",
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      renderCell: (params) => <RenderDateCell date={params.row.updated_at} />,
    },
    {
      type: "actions",
      field: "actions",
      headerName: "Actions",
      flex: 1,
      minWidth: 120,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      getActions: (params) => [
        <GridActionsLinkItem
          showInMenu
          icon={<Iconify icon="solar:pen-bold" />}
          label="Edit"
          href={paths.main.roles.edit(params.row.id)}
        />,
        <GridActionsCellItem
          showInMenu
          icon={<Iconify icon="solar:trash-bin-trash-bold" />}
          label="Delete"
          onClick={() => setDeleteId(params.row.id)}
          sx={{ color: "error.main" }}
        />,
      ],
    },
  ];

  const renderConfirmDialog = () => (
    <ConfirmDialog
      open={!!deleteId}
      onClose={() => setDeleteId("")}
      title="Delete Role"
      content={<> Are you sure you want to delete this role? </>}
      action={
        <Button
          variant="contained"
          color="error"
          onClick={() => {
            handleDeleteRow();
          }}
        >
          Delete
        </Button>
      }
    />
  );

  return (
    <>
      <DashboardContent
        sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}
      >
        <CustomBreadcrumbs
          heading="Roles Management"
          links={[
            { name: "Dashboard", href: paths.main.root },
            { name: "Roles" },
          ]}
          action={
            <>
              <Button
                component={RouterLink}
                href={paths.main.roles.permissions}
                variant="outlined"
                startIcon={<Iconify icon="solar:shield-check-bold" />}
                sx={{ mr: 1 }}
              >
                Manage Permissions
              </Button>
              <Button
                component={RouterLink}
                href={paths.main.roles.new}
                variant="contained"
                startIcon={<Iconify icon="mingcute:add-line" />}
              >
                New Role
              </Button>
            </>
          }
          sx={{ mb: { xs: 3, md: 5 } }}
        />

        <Card
          sx={{
            flexGrow: { md: 1 },
            display: { md: "flex" },
            height: { xs: 800, md: "1px" },
            flexDirection: { md: "column" },
          }}
        >
          <TableData
            tableData={tableData}
            columns={columns}
            productsLoading={rolesLoading}
          />
        </Card>

        {/* Snackbar Notification */}
        <Snackbar
          open={!!successMessage || !!errorMessage}
          autoHideDuration={3000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "top", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={errorMessage ? "error" : "success"}
            variant="filled"
          >
            {successMessage || errorMessage}
          </Alert>
        </Snackbar>
      </DashboardContent>

      {renderConfirmDialog()}
    </>
  );
}
