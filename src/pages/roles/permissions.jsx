import { useState, useEffect, useCallback } from "react";
import {
  Card,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Typography,
  Box,
  Button,
  Snackbar,
  Alert,
  CircularProgress,
  Paper,
  Chip,
} from "@mui/material";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import { paths } from "../../routes/paths";
import {
  useGetRoleListMutation,
  useGetPermissionListMutation,
  useGetRolePermissionsMutation,
  useUpdateRolePermissionsMutation,
} from "../../services/roleApi";

export default function RolePermissions() {
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [rolePermissions, setRolePermissions] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  const [getRoleList] = useGetRoleListMutation();
  const [getPermissionList] = useGetPermissionListMutation();
  const [getRolePermissions] = useGetRolePermissionsMutation();
  const [updateRolePermissions] = useUpdateRolePermissionsMutation();

  // Load initial data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load roles, permissions, and current role-permission mappings
      const [rolesResponse, permissionsResponse, rolePermissionsResponse] = await Promise.all([
        getRoleList().unwrap(),
        getPermissionList().unwrap(),
        getRolePermissions().unwrap(),
      ]);

      setRoles(rolesResponse.data?.roles || []);
      setPermissions(permissionsResponse.data?.permissions || []);
      
      // Convert role permissions to a matrix format
      const permissionMatrix = {};
      if (rolePermissionsResponse.data?.rolePermissions) {
        rolePermissionsResponse.data.rolePermissions.forEach((rp) => {
          if (!permissionMatrix[rp.roleId]) {
            permissionMatrix[rp.roleId] = {};
          }
          permissionMatrix[rp.roleId][rp.permissionId] = true;
        });
      }
      setRolePermissions(permissionMatrix);
      
    } catch (error) {
      console.error("Error loading data:", error);
      setErrorMessage("Failed to load role permissions data");
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionToggle = useCallback((roleId, permissionId) => {
    setRolePermissions(prev => ({
      ...prev,
      [roleId]: {
        ...prev[roleId],
        [permissionId]: !prev[roleId]?.[permissionId]
      }
    }));
  }, []);

  const handleSaveChanges = async () => {
    try {
      setSaving(true);
      
      // Convert matrix back to array format for API
      const rolePermissionUpdates = [];
      Object.keys(rolePermissions).forEach(roleId => {
        Object.keys(rolePermissions[roleId]).forEach(permissionId => {
          if (rolePermissions[roleId][permissionId]) {
            rolePermissionUpdates.push({
              roleId: parseInt(roleId),
              permissionId: parseInt(permissionId)
            });
          }
        });
      });

      await updateRolePermissions({ rolePermissions: rolePermissionUpdates }).unwrap();
      setSuccessMessage("Role permissions updated successfully!");
      
    } catch (error) {
      console.error("Error updating permissions:", error);
      setErrorMessage("Failed to update role permissions");
    } finally {
      setSaving(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSuccessMessage("");
    setErrorMessage("");
  };

  const isPermissionGranted = (roleId, permissionId) => {
    return rolePermissions[roleId]?.[permissionId] || false;
  };

  const getPermissionCount = (roleId) => {
    return Object.values(rolePermissions[roleId] || {}).filter(Boolean).length;
  };

  if (loading) {
    return (
      <DashboardContent>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </DashboardContent>
    );
  }

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Role Permissions Management"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Role Permissions" },
        ]}
        action={
          <Button
            variant="contained"
            onClick={handleSaveChanges}
            disabled={saving}
            startIcon={saving ? <CircularProgress size={20} /> : null}
          >
            Save Changes
          </Button>
        }
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card sx={{ overflow: "hidden" }}>
        <Box sx={{ p: 3, borderBottom: 1, borderColor: "divider" }}>
          <Typography variant="h6" gutterBottom>
            Permission Matrix
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage permissions for each role. Check the boxes to grant permissions.
          </Typography>
        </Box>

        <TableContainer component={Paper} sx={{ maxHeight: "70vh" }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell 
                  sx={{ 
                    minWidth: 200, 
                    fontWeight: "bold",
                    backgroundColor: "grey.50",
                    position: "sticky",
                    left: 0,
                    zIndex: 1
                  }}
                >
                  Permissions
                </TableCell>
                {roles.map((role) => (
                  <TableCell 
                    key={role.id} 
                    align="center" 
                    sx={{ 
                      minWidth: 120,
                      fontWeight: "bold",
                      backgroundColor: "grey.50"
                    }}
                  >
                    <Box>
                      <Typography variant="subtitle2" noWrap>
                        {role.roleDisplayName}
                      </Typography>
                      <Chip 
                        label={`${getPermissionCount(role.id)} permissions`}
                        size="small"
                        color="primary"
                        variant="outlined"
                        sx={{ mt: 0.5 }}
                      />
                    </Box>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {permissions.map((permission) => (
                <TableRow key={permission.id} hover>
                  <TableCell 
                    sx={{ 
                      fontWeight: "medium",
                      backgroundColor: "background.paper",
                      position: "sticky",
                      left: 0,
                      zIndex: 1,
                      borderRight: 1,
                      borderColor: "divider"
                    }}
                  >
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {permission.name}
                      </Typography>
                      {permission.description && (
                        <Typography variant="caption" color="text.secondary">
                          {permission.description}
                        </Typography>
                      )}
                    </Box>
                  </TableCell>
                  {roles.map((role) => (
                    <TableCell key={`${role.id}-${permission.id}`} align="center">
                      <Checkbox
                        checked={isPermissionGranted(role.id, permission.id)}
                        onChange={() => handlePermissionToggle(role.id, permission.id)}
                        color="primary"
                      />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      <Snackbar
        open={!!successMessage || !!errorMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={errorMessage ? "error" : "success"}
          variant="filled"
        >
          {successMessage || errorMessage}
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
}
