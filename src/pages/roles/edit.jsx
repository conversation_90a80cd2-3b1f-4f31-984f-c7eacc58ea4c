import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z as zod } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useParams } from "react-router";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import {
  Card,
  TextField,
  Button,
  Box,
  Snackbar,
  Alert,
  CircularProgress,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import {
  useUpdateRoleMutation,
  useGetRoleListMutation,
} from "../../services/roleApi";
import { useRouter } from "../../components/routes/hooks";

const EditRoleSchema = zod.object({
  roleDisplayName: zod.string().min(1, "Role name is required."),
  description: zod.string().optional(),
});

export default function RoleEdit() {
  const { id } = useParams();
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState(false);
  const [roleData, setRoleData] = useState(null);

  const [updateRole, { isLoading }] = useUpdateRoleMutation();
  const [getRoleList, { data: rolesData, isLoading: isRoleDataLoading }] =
    useGetRoleListMutation();

  useEffect(() => {
    getRoleList();
  }, [getRoleList]);

  const methods = useForm({
    resolver: zodResolver(EditRoleSchema),
    defaultValues: {
      roleDisplayName: "",
      description: "",
    },
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = methods;

  useEffect(() => {
    if (rolesData?.data?.roles) {
      const role = rolesData.data.roles.find(r => r.id === parseInt(id));
      if (role) {
        setRoleData(role);
        reset({
          roleDisplayName: role.roleDisplayName || "",
          description: role.description || "",
        });
      }
    }
  }, [rolesData, id, reset]);

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const onSubmit = async (data) => {
    try {
      await updateRole({ roleId: id, ...data }).unwrap();
      setSuccessMessage(true);
      setTimeout(() => {
        router.push(paths.main.roles.list);
      }, 1000);
    } catch (error) {
      setErrorMessage(error.data?.message || "Failed to update role.");
    }
  };

  const onInvalid = (errors) => {
    console.log('Form validation errors:', errors);
  };

  if (isRoleDataLoading) {
    return (
      <DashboardContent>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </DashboardContent>
    );
  }

  if (!roleData) {
    return (
      <DashboardContent>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <Alert severity="error">Role not found</Alert>
        </Box>
      </DashboardContent>
    );
  }

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Edit Role"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Roles", href: paths.main.roles.list },
          { name: "Edit Role" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 600,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
        }}
      >
        <Form
          methods={methods}
          onSubmit={handleSubmit(onSubmit, onInvalid)}
        >
          <Box mt={3}>
            <Grid container spacing={3}>
              <Grid size={12}>
                <TextField
                  {...register("roleDisplayName")}
                  fullWidth
                  label="Role Name"
                  error={!!errors.roleDisplayName}
                  helperText={errors.roleDisplayName?.message}
                  placeholder="e.g., Admin, Editor, Viewer"
                />
              </Grid>
              <Grid size={12}>
                <TextField
                  {...register("description")}
                  fullWidth
                  label="Description"
                  multiline
                  rows={4}
                  error={!!errors.description}
                  helperText={errors.description?.message}
                  placeholder="Describe the role and its responsibilities..."
                />
              </Grid>
              <Grid size={12} display="flex" justifyContent="flex-end" gap={2}>
                <Button
                  variant="outlined"
                  onClick={() => router.push(paths.main.roles.list)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={isLoading}
                  color="primary"
                  size="large"
                  startIcon={isLoading ? <CircularProgress size={20} /> : null}
                >
                  Update Role
                </Button>
              </Grid>
            </Grid>
          </Box>
        </Form>
      </Card>

      <Snackbar
        open={successMessage || !!errorMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={errorMessage ? "error" : "success"}
          variant="filled"
        >
          {errorMessage || "Role updated successfully."}
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
}
