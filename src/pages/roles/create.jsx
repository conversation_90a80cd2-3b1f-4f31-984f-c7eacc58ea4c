import { useState } from "react";
import { useForm } from "react-hook-form";
import { z as zod } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import {
  Card,
  TextField,
  Button,
  Box,
  Snackbar,
  Alert,
  CircularProgress,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import { useCreateRoleMutation } from "../../services/roleApi";
import { useRouter } from "../../components/routes/hooks";

const CreateRoleSchema = zod.object({
  roleDisplayName: zod.string().min(1, "Role name is required."),
  description: zod.string().optional(),
});

export default function RoleCreate() {
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState(false);
  const [createRole, { isLoading }] = useCreateRoleMutation();

  const methods = useForm({
    resolver: zodResolver(CreateRoleSchema),
    defaultValues: {
      roleDisplayName: "",
      description: "",
    },
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = methods;

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const onSubmit = async (data) => {
    try {
      await createRole(data).unwrap();
      setSuccessMessage(true);
      reset();
      setTimeout(() => {
        router.push(paths.main.roles.list);
      }, 1000);
    } catch (error) {
      setErrorMessage(error.data?.message || "Failed to create role.");
    }
  };

  const onInvalid = (errors) => {
    console.log('Form validation errors:', errors);
  };

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Create New Role"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Roles", href: paths.main.roles.list },
          { name: "New Role" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 600,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
        }}
      >
        <Form
          methods={methods}
          onSubmit={handleSubmit(onSubmit, onInvalid)}
        >
          <Box mt={3}>
            <Grid container spacing={3}>
              <Grid size={12}>
                <TextField
                  {...register("roleDisplayName")}
                  fullWidth
                  label="Role Name"
                  error={!!errors.roleDisplayName}
                  helperText={errors.roleDisplayName?.message}
                  placeholder="e.g., Admin, Editor, Viewer"
                />
              </Grid>
              <Grid size={12}>
                <TextField
                  {...register("description")}
                  fullWidth
                  label="Description"
                  multiline
                  rows={4}
                  error={!!errors.description}
                  helperText={errors.description?.message}
                  placeholder="Describe the role and its responsibilities..."
                />
              </Grid>
              <Grid size={12} display="flex" justifyContent="flex-end" gap={2}>
                <Button
                  variant="outlined"
                  onClick={() => router.push(paths.main.roles.list)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={isLoading}
                  color="primary"
                  size="large"
                  startIcon={isLoading ? <CircularProgress size={20} /> : null}
                >
                  Create Role
                </Button>
              </Grid>
            </Grid>
          </Box>
        </Form>
      </Card>

      <Snackbar
        open={successMessage || !!errorMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={errorMessage ? "error" : "success"}
          variant="filled"
        >
          {errorMessage || "Role created successfully."}
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
}
