import { useState } from "react";
import {
  <PERSON>,
  Typography,
  TextField,
  MenuItem,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormHelperText,
  FormControl,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { paths } from "../../routes/paths";
import { useDownloadReportMutation } from "../../services/reportApi"; // Adjust path as needed

export default function DownloadReport() {
  const [reportType, setReportType] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [exportType, setExportType] = useState("");
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState("");

  const [downloadReport, { isLoading }] = useDownloadReportMutation();

  const handleDownload = async () => {
    let tempErrors = {};

    if (!reportType) tempErrors.reportType = "Report Type is required.";
    if (!exportType) tempErrors.exportType = "Export Type is required.";
    if ((startDate && !endDate) || (!startDate && endDate)) {
      tempErrors.dateRange =
        "Start Date and End Date are required if date range filtering is chosen.";
    }

    if (Object.keys(tempErrors).length > 0) {
      setErrors(tempErrors);
      setSuccess("");
      return;
    }

    try {
      setErrors({});
      setSuccess("");

      const payload = {
        reportType,
        exportType,
        ...(startDate &&
          endDate && {
            startDate,
            endDate,
          }),
      };

      const response = await downloadReport(payload).unwrap();
      const res = await downloadReport(payload).unwrap();
      console.log("Download response:", res);

      const blob = new Blob([response], {
        type:
          exportType === "Excel"
            ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            : "text/csv",
      });

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `report-${reportType}.${exportType === "Excel" ? "xlsx" : "csv"}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove();

      setSuccess("Report downloaded successfully.");
    } catch (err) {
      console.error("Download failed:", err);
      setErrors({
        api:
          err?.data?.message ||
          err.message ||
          "Something went wrong during download.",
      });
    }
  };

  return (
    <Grid size={{ xs: 12, md: 12 }}>
      <Card sx={{ height: "100%", p: 3 }}>
        <CustomBreadcrumbs
          heading="Download Report"
          links={[
            { name: "Dashboard", href: paths.main.root },
            { name: "Download Report" },
          ]}
          sx={{ mb: { xs: 3, md: 5 } }}
        />

        <Grid container spacing={2}>
          {/* Report Type */}
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              select
              label="Report Type"
              fullWidth
              value={reportType}
              onChange={(e) => setReportType(e.target.value)}
              error={Boolean(errors.reportType)}
              helperText={errors.reportType}
            >
              <MenuItem value="user">User Analytics</MenuItem>
              <MenuItem value="ad">Ad Analytics</MenuItem>
            </TextField>
          </Grid>

          {/* Start Date */}
          <Grid size={{ xs: 12, md: 3 }}>
            <TextField
              label="Start Date"
              type="date"
              fullWidth
              slotProps={{
                inputLabel: { shrink: true },
              }}
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
            />
          </Grid>

          {/* End Date */}
          <Grid size={{ xs: 12, md: 3 }}>
            <TextField
              label="End Date"
              type="date"
              fullWidth
              slotProps={{
                inputLabel: { shrink: true },
              }}
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
            />
          </Grid>

          {errors.dateRange && (
            <Grid size={12}>
              <FormHelperText error>{errors.dateRange}</FormHelperText>
            </Grid>
          )}

          {/* Export Type */}
          <Grid size={{ xs: 12, md: 6 }}>
            <FormControl error={Boolean(errors.exportType)}>
              <Typography variant="subtitle1" gutterBottom>
                Export Type
              </Typography>
              <RadioGroup
                row
                value={exportType}
                onChange={(e) => setExportType(e.target.value)}
              >
                <FormControlLabel
                  value="Excel"
                  control={<Radio />}
                  label="Excel (.xlsx)"
                />
                <FormControlLabel
                  value="CSV"
                  control={<Radio />}
                  label="CSV (.csv)"
                />
              </RadioGroup>
              <FormHelperText>{errors.exportType}</FormHelperText>
            </FormControl>
          </Grid>

          {/* Download Button */}
          <Grid size={12}>
            <Button
              variant="contained"
              onClick={handleDownload}
              disabled={isLoading}
            >
              {isLoading ? "Downloading..." : "Download"}
            </Button>
          </Grid>

          {/* Success or API Error Message */}
          {success && (
            <Grid size={12}>
              <Typography color="success.main">{success}</Typography>
            </Grid>
          )}
          {errors.api && (
            <Grid size={12}>
              <Typography color="error.main">{errors.api}</Typography>
            </Grid>
          )}
        </Grid>
      </Card>
    </Grid>
  );
}
