
import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Snackbar } from "@mui/material";
import { GridActionsCellItem } from "@mui/x-data-grid";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { ConfirmDialog } from "../../components/custom-dialog";
import { Iconify } from "../../components/iconify";
import { DashboardContent } from "../../layout/dashboard/content";
import { paths } from "../../routes/paths";
import { RouterLink } from "../../components/routes/components";
import {
  useDeleteCmsMutation,
  useGetCmsListMutation,
} from "../../services/cmsApi";
import {
  GridActionsLinkItem,
  RenderDateCell,
  RenderStatus,
  RenderText,
} from "../../components/table/common";
import { TableData } from "../../components/table/table-content";

export default function CmsList() {
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [deleteId, setDeleteId] = useState("");
  const [tableData, setTableData] = useState([]);
  const [getCmsList, { data: cmsData, isLoading: cmsLoading }] =
    useGetCmsListMutation();
  const [deleteCms] = useDeleteCmsMutation();

  useEffect(() => {
    getCmsList();
  }, []);

  useEffect(() => {
    if (cmsData?.data?.cms?.length) {
      setTableData(cmsData.data.cms);
    }
  }, [cmsData]);

  const handleDeleteRow = useCallback(async () => {
    try {
      await deleteCms({ id: deleteId }).unwrap();
      setSuccessMessage("CMS content deleted successfully!");
      setDeleteId("");
      getCmsList();
    } catch (error) {
      setErrorMessage("Failed to delete CMS content.");
    }
  }, [deleteCms, deleteId, getCmsList]);

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const columns = [
    {
      field: "id",
      headerName: "ID",
      flex: 1,
      minWidth: 80,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.id} />,
    },
    {
      field: "title",
      headerName: "Title",
      flex: 2,
      minWidth: 200,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.title} />,
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      minWidth: 110,
      disableColumnMenu: true,
      renderCell: (params) => <RenderStatus status={params.row.status} />,
    },
    {
      field: "created_at",
      headerName: "Created At",
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      renderCell: (params) => <RenderDateCell date={params.row.created_at} />,
    },
    {
      field: "updated_at",
      headerName: "Updated At",
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      renderCell: (params) => <RenderDateCell date={params.row.updated_at} />,
    },
    {
      type: "actions",
      field: "actions",
      headerName: "Actions",
      flex: 1,
      minWidth: 80,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      getActions: (params) => [
        <GridActionsLinkItem
          showInMenu
          icon={<Iconify icon="solar:eye-bold" />}
          label="View"
          href={paths.main.cms.details(params.row.id)}
        />,
        <GridActionsLinkItem
          showInMenu
          icon={<Iconify icon="solar:pen-bold" />}
          label="Edit"
          href={paths.main.cms.edit(params.row.id)}
        />,
        <GridActionsCellItem
          showInMenu
          icon={<Iconify icon="solar:trash-bin-trash-bold" />}
          label="Delete"
          onClick={() => setDeleteId(params.row.id)}
          sx={{ color: "error.main" }}
        />,
      ],
    },
  ];

  const renderConfirmDialog = () => (
    <ConfirmDialog
      open={!!deleteId}
      onClose={() => setDeleteId("")}
      title="Delete"
      content={<> Are you sure want to delete this CMS content? </>}
      action={
        <Button
          variant="contained"
          color="error"
          onClick={() => {
            handleDeleteRow();
          }}
        >
          Delete
        </Button>
      }
    />
  );

  return (
    <>
      <DashboardContent
        sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}
      >
        <CustomBreadcrumbs
          heading="CMS Content"
          links={[
            { name: "Dashboard", href: paths.main.root },
            { name: "CMS Content" },
          ]}
          action={
            <Button
              component={RouterLink}
              href={paths.main.cms.new}
              variant="contained"
              startIcon={<Iconify icon="mingcute:add-line" />}
            >
              New CMS Content
            </Button>
          }
          sx={{ mb: { xs: 3, md: 5 } }}
        />

        <Card
          sx={{
            flexGrow: { md: 1 },
            display: { md: "flex" },
            height: { xs: 800, md: "1px" },
            flexDirection: { md: "column" },
          }}
        >
          <TableData
            tableData={tableData}
            columns={columns}
            productsLoading={cmsLoading}
          />
        </Card>

        {/* Snackbar Notification */}
        <Snackbar
          open={!!successMessage || !!errorMessage}
          autoHideDuration={3000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "top", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={errorMessage ? "error" : "success"}
            variant="filled"
          >
            {successMessage || errorMessage}
          </Alert>
        </Snackbar>
      </DashboardContent>

      {renderConfirmDialog()}
    </>
  );
}