import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import {
  Card,
  TextField,
  Box,
  CircularProgress,
  Typography,
  Paper,
  Chip,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import { useGetCmsDetailMutation } from "../../services/cmsApi";
import { useParams } from "react-router";
import { fDate, fTime } from "../../utils/format-time";

export default function CmsView() {
  const { id } = useParams();
  const [getCmsDetail, { data: cmsData, isLoading }] =
    useGetCmsDetailMutation();

  const { watch, setValue } = useForm({});

  useEffect(() => {
    getCmsDetail({ cmsId: id });
  }, [getCmsDetail, id]);

  useEffect(() => {
    if (cmsData?.data) {
      Object.entries(cmsData.data).forEach(([key, value]) => {
        setValue(key, value);
      });
    }
  }, [cmsData, setValue]);

  const getStatusColor = (status) => {
    switch (status) {
      case "Active":
        return "success";
      case "Inactive":
        return "error";
      case "Draft":
        return "warning";
      default:
        return "default";
    }
  };

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="CMS Content Details"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "CMS Content", href: paths.main.cms.list },
          { name: "Content Details" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
          display: "flex",
          alignItems: isLoading ? "center" : "flex-start",
          justifyContent: isLoading ? "center" : "flex-start",
        }}
      >
        {isLoading ? (
          <CircularProgress />
        ) : (
          <Form>
            <Box mt={3} width="100%">
              <Grid container spacing={2}>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Title"
                    value={watch("title") || "----"}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Slug"
                    value={watch("slug") || "----"}
                  />
                </Grid>
                <Grid size={12}>
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Status
                    </Typography>
                    <Chip
                      label={watch("status") || "----"}
                      color={getStatusColor(watch("status"))}
                      variant="filled"
                    />
                  </Box>
                </Grid>
                <Grid size={12}>
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Content
                    </Typography>
                    <Paper
                      variant="outlined"
                      sx={{
                        p: 2,
                        minHeight: 200,
                        backgroundColor: "grey.50",
                      }}
                    >
                      <div
                        dangerouslySetInnerHTML={{
                          __html:
                            watch("content") || "<p>No content available</p>",
                        }}
                        style={{
                          lineHeight: 1.6,
                          color: "#333",
                        }}
                      />
                    </Paper>
                  </Box>
                </Grid>
                <Grid size={6}>
                  <TextField
                    fullWidth
                    disabled
                    label="Created At"
                    value={
                      watch("created_at")
                        ? `${fDate(watch("created_at"))} ${fTime(watch("created_at"))}`
                        : "----"
                    }
                  />
                </Grid>
                <Grid size={6}>
                  <TextField
                    fullWidth
                    disabled
                    label="Updated At"
                    value={
                      watch("updated_at")
                        ? `${fDate(watch("updated_at"))} ${fTime(watch("updated_at"))}`
                        : "----"
                    }
                  />
                </Grid>
              </Grid>
            </Box>
          </Form>
        )}
      </Card>
    </DashboardContent>
  );
}
