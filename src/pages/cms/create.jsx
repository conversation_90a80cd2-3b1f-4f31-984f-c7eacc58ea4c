import { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { z as zod } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import {
  Card,
  TextField,
  Button,
  Box,
  Snackbar,
  Alert,
  CircularProgress,
  MenuItem,
  FormHelperText,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import { useCreateCmsMutation } from "../../services/cmsApi";
import { useRouter } from "../../components/routes/hooks";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

const CreateCmsSchema = zod.object({
  title: zod.string().min(1, "Title is required."),
  content: zod.string().min(1, "Content is required."),
  status: zod.enum(["Active", "Inactive"], {
    message: "Invalid status selected.",
  }),
});

export default function CmsCreate() {
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState(false);
  const [createCms, { isLoading }] = useCreateCmsMutation();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    control,
  } = useForm({
    resolver: zodResolver(CreateCmsSchema),
    defaultValues: {
      title: "",
      content: "",
      status: "Active",
    },
  });

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const onSubmit = async (data) => {
    try {
      await createCms(data).unwrap();
      setSuccessMessage(true);
      reset();
      setTimeout(() => {
        router.push("/cms");
      }, 1000);
    } catch (error) {
      setErrorMessage(error.data?.message || "Failed to create CMS content.");
    }
  };

  // Rich text editor modules configuration
  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ indent: "-1" }, { indent: "+1" }],
      [{ align: [] }],
      ["link", "image"],
      [{ color: [] }, { background: [] }],
      ["clean"],
    ],
  };

  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "list",
    "bullet",
    "indent",
    "align",
    "link",
    "image",
    "color",
    "background",
  ];

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Create CMS Content"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "CMS Content", href: paths.main.cms.list },
          { name: "New Content" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
        }}
      >
        <Form
          methods={{ handleSubmit, register }}
          onSubmit={handleSubmit(onSubmit)}
        >
          <Box mt={3}>
            <Grid container spacing={2}>
              <Grid size={12}>
                <TextField
                  {...register("title")}
                  fullWidth
                  label="Title"
                  error={!!errors.title}
                  helperText={errors.title?.message}
                />
              </Grid>
              <Grid size={12}>
                <Box>
                  <Controller
                    name="content"
                    control={control}
                    render={({ field }) => (
                      <Box>
                        <ReactQuill
                          theme="snow"
                          value={field.value}
                          onChange={field.onChange}
                          modules={modules}
                          formats={formats}
                          style={{
                            height: "300px",
                            marginBottom: "50px",
                          }}
                          placeholder="Enter your content here..."
                        />
                        {errors.content && (
                          <FormHelperText error sx={{ mt: 1 }}>
                            {errors.content.message}
                          </FormHelperText>
                        )}
                      </Box>
                    )}
                  />
                </Box>
              </Grid>
              <Grid size={12}>
                <TextField
                  select
                  {...register("status")}
                  fullWidth
                  label="Status"
                  error={!!errors.status}
                  helperText={errors.status?.message}
                  value={watch("status") || "Active"}
                >
                  <MenuItem value="Active">Active</MenuItem>
                  <MenuItem value="Inactive">Inactive</MenuItem>
                </TextField>
              </Grid>
              <Grid size={12} display="flex" justifyContent="flex-end">
                <Button
                  type="submit"
                  variant="contained"
                  disabled={isLoading}
                  color="primary"
                  size="large"
                  startIcon={isLoading ? <CircularProgress size={20} /> : null}
                >
                  Create Content
                </Button>
              </Grid>
            </Grid>
          </Box>
        </Form>
      </Card>

      <Snackbar
        open={successMessage || !!errorMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={errorMessage ? "error" : "success"}
          variant="filled"
        >
          {errorMessage || "CMS content created successfully."}
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
}
