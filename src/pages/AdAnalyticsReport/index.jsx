import React, { useState } from "react";
import {
  Card,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Button,
  FormHelperText,
  Box,
  Snackbar,
  Alert,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { paths } from "../../routes/paths";
import { DatePicker } from "@mui/x-date-pickers";
import { useDownloadReportMutation } from "../../services/reportApi";

const adTypes = ["Display", "Video", "Banner"];
const exportTypes = ["Excel", "CSV"];

const AdAnalyticsReport = () => {
  const [formData, setFormData] = useState({
    reportType: "ad",
    exportType: "",
    startDate: null,
    endDate: null,
  });

  const [errors, setErrors] = useState({});
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarError, setSnackbarError] = useState("");

  // Use RTK Query mutation hook
  const [downloadReport, { isLoading }] = useDownloadReportMutation();

  // Validate only exportType and date range
  const validate = () => {
    const newErrors = {};
    if (!formData.exportType) newErrors.exportType = "Export Type is required.";
    if (
      (formData.startDate && !formData.endDate) ||
      (!formData.startDate && formData.endDate)
    ) {
      newErrors.dateRange =
        "Start Date and End Date are required if filtering by date.";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleDownload = async () => {
    if (!validate()) return;

    try {
      // Prepare payload
      const payload = {
        reportType: formData.reportType || "ad",
        exportType: formData.exportType,
        startDate: formData.startDate?.toISOString() || null,
        endDate: formData.endDate?.toISOString() || null,
      };

      const response = await downloadReport(payload).unwrap();

      const blob = new Blob([response], { type: response.type || "application/octet-stream" });

      const filename = "ad_analytics_report." + (formData.exportType.toLowerCase() || "xlsx");

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      setSnackbarOpen(true);
    } catch (error) {
      setSnackbarError(error?.data?.message || error?.error || "Failed to download report.");
    }
  };

  return (
    <Grid size={12}>
      <Card sx={{ p: 3 }}>
        <CustomBreadcrumbs
          heading="Ad Analytics Report"
          links={[
            { name: "Dashboard", href: paths.main.root },
            { name: "Ad Analytics Report" },
          ]}
          sx={{ mb: { xs: 3, md: 5 } }}
        />
        <Grid container spacing={2}>
          {/* Report Type (optional) */}
          <Grid size={{ xs: 12, md: 4 }}>
            <FormControl fullWidth>
              <InputLabel>Report Type</InputLabel>
              <Select
                // value={formData.reportType}
                // onChange={(e) =>
                //   setFormData({ ...formData, reportType:e.target.value })
                // }
                label="Report Type"
              >
                <MenuItem value="">All</MenuItem>
                {adTypes.map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Export Type (required) */}
          <Grid size={{ xs: 12, md: 4 }}>
            <FormControl fullWidth error={!!errors.exportType}>
              <InputLabel>Export Type *</InputLabel>
              <Select
                value={formData.exportType}
                onChange={(e) =>
                  setFormData({ ...formData, exportType: e.target.value })
                }
                label="Export Type *"
              >
                {exportTypes.map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </Select>
              {errors.exportType && (
                <FormHelperText>{errors.exportType}</FormHelperText>
              )}
            </FormControl>
          </Grid>

          {/* Date Range */}
          <Grid size={{ xs: 12, md: 4 }}>
            <Box display="flex" gap={2}>
              <DatePicker
                label="Start Date"
                value={formData.startDate}
                onChange={(newDate) =>
                  setFormData({ ...formData, startDate: newDate })
                }
              />
              <DatePicker
                label="End Date"
                value={formData.endDate}
                onChange={(newDate) =>
                  setFormData({ ...formData, endDate: newDate })
                }
              />
            </Box>
            {errors.dateRange && (
              <FormHelperText error sx={{ mt: 1 }}>
                {errors.dateRange}
              </FormHelperText>
            )}
          </Grid>

          {/* Download Button */}
          <Grid size={12} mt={2}>
            <Button variant="contained" onClick={handleDownload} disabled={isLoading}>
              {isLoading ? "Downloading..." : "Download Report"}
            </Button>
          </Grid>
        </Grid>

        {/* Snackbar: Success */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={4000}
          onClose={() => setSnackbarOpen(false)}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        >
          <Alert
            onClose={() => setSnackbarOpen(false)}
            severity="success"
            sx={{ width: "100%" }}
          >
            Ad Analytics report downloaded successfully.
          </Alert>
        </Snackbar>

        {/* Snackbar: Error */}
        <Snackbar
          open={!!snackbarError}
          autoHideDuration={4000}
          onClose={() => setSnackbarError("")}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        >
          <Alert
            onClose={() => setSnackbarError("")}
            severity="error"
            sx={{ width: "100%" }}
          >
            {snackbarError}
          </Alert>
        </Snackbar>
      </Card>
    </Grid>
  );
};

export default AdAnalyticsReport;