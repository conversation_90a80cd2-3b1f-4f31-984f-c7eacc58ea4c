import React, { useEffect, useState } from "react";
import {
  Card,
  TextField,
  Button,
  Typography,
  Box,
  Snackbar,
  Alert,
  MenuItem,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useNavigate } from "react-router-dom";
import { DashboardContent } from "../../layout/dashboard/content";
import { useSelector } from "react-redux";
import { useGetRoleListMutation } from "../../services/roleApi";
import { useUpdateProfileMutation } from "../../services/userApi";

export default function MyProfile() {
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    email: "",
    role: "",
  });
  const user = useSelector((state) => state.auth.user);
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState(false);

  const [getRoleList, { data: roleList }] = useGetRoleListMutation();
  const [updateProfile, { isLoading }] = useUpdateProfileMutation();

  useEffect(() => {
    getRoleList();
  }, [getRoleList]);

  useEffect(() => {
    if (user) {
      setFormData({
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        email: user.email || "",
        role: user.role || "",
      });
    }
  }, [user]);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setErrors({ ...errors, [e.target.name]: "" });
  };

  const validateForm = () => {
    let newErrors = {};
    if (!formData.first_name) newErrors.first_name = "First Name is required.";
    if (!formData.last_name) newErrors.last_name = "Last Name is required.";
    if (!formData.email) {
      newErrors.email = "Email is required.";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Enter a valid email address.";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    try {
      const result = await updateProfile(formData).unwrap();
      setSuccessMessage(true);
    } catch (error) {
      const apiErrors = error?.data?.errors || {};
      setErrors(apiErrors);
    }
  };

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
  };

  const navigate = useNavigate();

  const handleChangePassword = () => {
    navigate("/change-password");
  };

  return (
    <DashboardContent
      sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}
    >
      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
        }}
      >
        <Typography
          variant="h5"
          fontWeight="bold"
          textAlign="center"
          gutterBottom
        >
          My Account
        </Typography>

        <Box component="form" onSubmit={handleSubmit} mt={3}>
          <Grid container spacing={2}>
            {/* Name Field */}
            <Grid size={12}>
              <TextField
                fullWidth
                label="First Name"
                name="first_name"
                variant="outlined"
                value={formData.first_name}
                onChange={handleChange}
                error={!!errors.first_name}
                helperText={errors.first_name}
              />
            </Grid>
            <Grid size={12}>
              <TextField
                fullWidth
                label="Last Name"
                name="last_name"
                variant="outlined"
                value={formData.last_name}
                onChange={handleChange}
                error={!!errors.last_name}
                helperText={errors.last_name}
              />
            </Grid>

            <Grid size={12}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                variant="outlined"
                value={formData.email}
                onChange={handleChange}
                error={!!errors.email}
                helperText={errors.email}
              />
            </Grid>

            {/* Role Field */}
            <Grid size={12}>
              <TextField
                select
                name="role"
                fullWidth
                label="Role"
                variant="outlined"
                value={
                  roleList?.data?.roles?.some(
                    (r) => String(r.id) === String(formData.role),
                  )
                    ? String(formData.role)
                    : ""
                }
                onChange={handleChange}
                error={!!errors.role}
                helperText={errors.role}
              >
                {roleList?.data?.roles?.length ? (
                  roleList.data.roles.map((role) => (
                    <MenuItem key={role.id} value={role.id}>
                      {role.roleDisplayName}
                    </MenuItem>
                  ))
                ) : (
                  <MenuItem disabled>No roles available</MenuItem>
                )}
              </TextField>
            </Grid>

            {/* Buttons */}
            <Grid size={12} display="flex" justifyContent="flex-end">
              <Box>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  size="large"
                  disabled={isLoading}
                >
                  {isLoading ? "Updating..." : "Update Profile"}
                </Button>

                <Button
                  type="button"
                  variant="contained"
                  size="large"
                  sx={{
                    backgroundColor: "#FFC107",
                    ml: 2,
                  }}
                  onClick={handleChangePassword}
                >
                  Change Password
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Card>

      <Snackbar
        open={successMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }} // Position of Snackbar
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity="success"
          variant="filled"
        >
          Profile updated successfully.
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
}
