import { useState } from "react";
import { useForm } from "react-hook-form";
import { z as zod } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import {
  Card,
  TextField,
  Button,
  Box,
  Snackbar,
  Alert,
  CircularProgress,
  MenuItem,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import { useRouter } from "../../components/routes/hooks";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import {
  adTypes,
  AdPlacements,
  MAX_IMAGE_SIZE,
  MAX_VIDEO_SIZE,
} from "../../utils/constants";
import { useCreateAdsMutation } from "../../services/adsApi";
import { handleFileWithVideoCheck, uploadToS3 } from "../../utils/file-upload";
import InputFileUpload from "../../components/file-upload";
import { useSelector } from "react-redux";

const CreateAdSchema = zod.object({
  ad_title: zod.string().min(1, "Ad title is required"),
  advertiser_name: zod.string().min(1, "Advertiser name is required"),
  ad_type: zod.string().min(1, "Ad type is required"),
  ad_placement: zod.string().min(1, "Ad placement is required"),
  category: zod.string().optional(),
  target_audience: zod.string().optional(),
  start_date: zod.string().min(1, "Start date is required"),
  end_date: zod.string().min(1, "End date is required"),
  status: zod.enum(["Active", "Inactive"], {
    message: "Invalid status selected.",
  }),
  ad_image: zod
    .any()
    .refine((file) => !!file, {
      message: "Ad image is required",
    })
    .refine((file) => !!file && file.size <= MAX_IMAGE_SIZE, {
      message: "Image must be less than 2MB",
    })
    .refine(
      (file) =>
        file &&
        ["image/jpeg", "image/png", "image/gif"].includes(file.type) &&
        /\.(jpg|jpeg|png|gif)$/i.test(file.name),
      {
        message: "Only .jpg, .jpeg, .png, and .gif are allowed",
      },
    ),
  ad_media: zod
    .any()
    .refine((file) => !!file, {
      message: "Ad media is required",
    })
    .refine((file) => !!file && file.size <= MAX_VIDEO_SIZE, {
      message: "Video must be less than 20MB",
    })
    .refine(
      (file) => file && file.type === "video/mp4" && /\.mp4$/i.test(file.name),
      {
        message: "Only .mp4 videos are allowed",
      },
    ),
});

export default function AdsCreate() {
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState(false);
  const [createAds, { isLoading }] = useCreateAdsMutation();

  const accessToken = useSelector((state) => state.auth.accessToken);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm({
    resolver: zodResolver(CreateAdSchema),
    defaultValues: {
      ad_title: "",
      advertiser_name: "",
      ad_type: "",
      ad_image: null,
      ad_placement: "",
      category: "",
      target_audience: "",
      ad_media: null,
      start_date: dayjs(),
      end_date: dayjs(),
      status: "Active",
    },
  });

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const onSubmit = async (data) => {
    try {
      const adImageUrl = await uploadToS3(
        data.ad_image,
        accessToken,
        "/media/generate-presigned-url",
      );
      const adMediaURL = await uploadToS3(
        data.ad_media,
        accessToken,
        "/media/generate-presigned-url",
      );

      console.log(adImageUrl, adMediaURL);

      const payload = {
        ...data,
        ad_image: adImageUrl,
        ad_media: adMediaURL,
      };

      await createAds(payload).unwrap();
      setSuccessMessage(true);
      reset();
      setTimeout(() => {
        router.push("/ads");
      }, 1000);
    } catch (error) {
      console.log(error);
      setErrorMessage(error.data?.message || "Failed to create Ads.");
    }
  };

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Create a new Ads"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Ads", href: paths.main.ads.list },
          { name: "New Ad" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
        }}
      >
        <Form
          methods={{ handleSubmit, register }}
          onSubmit={handleSubmit(onSubmit)}
        >
          <Box mt={3}>
            <Grid container spacing={2}>
              <Grid size={12}>
                <TextField
                  {...register("ad_title")}
                  fullWidth
                  label="Ad Title"
                  error={!!errors.ad_title}
                  helperText={errors.ad_title?.message}
                />
              </Grid>
              <Grid size={12}>
                <TextField
                  {...register("advertiser_name")}
                  fullWidth
                  label="Advertiser Name"
                  error={!!errors.advertiser_name}
                  helperText={errors.advertiser_name?.message}
                />
              </Grid>
              <Grid size={12}>
                <TextField
                  select
                  {...register("ad_type")}
                  fullWidth
                  label="Ad Type"
                  error={!!errors.ad_type}
                  helperText={errors.ad_type?.message}
                  value={watch("ad_type") || ""}
                >
                  {adTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid size={12}>
                <InputFileUpload
                  name="ad_image"
                  label="Upload Ad Image"
                  value={watch("ad_image")}
                  onChange={(e) => setValue("ad_image", e.target.files?.[0])}
                  type="image"
                />
                {errors.ad_image && (
                  <p style={{ color: "red" }}>{errors.ad_image.message}</p>
                )}
              </Grid>
              <Grid size={12}>
                <TextField
                  select
                  {...register("ad_placement")}
                  fullWidth
                  label="Ad Placement"
                  error={!!errors.ad_placement}
                  helperText={errors.ad_placement?.message}
                  value={watch("ad_placement") || ""}
                >
                  {AdPlacements.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid size={12}>
                <TextField
                  {...register("category")}
                  fullWidth
                  label="Category"
                  error={!!errors.category}
                  helperText={errors.category?.message}
                />
              </Grid>
              <Grid size={12}>
                <InputFileUpload
                  name="ad_media"
                  label="Upload Ad Media"
                  value={watch("ad_media")}
                  onChange={(e) =>
                    handleFileWithVideoCheck({
                      file: e.target.files?.[0],
                      maxDuration: 30,
                      setValue,
                      setErrorMessage,
                    })
                  }
                />
                {errors.ad_media && (
                  <p style={{ color: "red" }}>{errors.ad_media.message}</p>
                )}
              </Grid>
              <Grid size={12}>
                <TextField
                  {...register("target_audience")}
                  fullWidth
                  label="Target Audience"
                  error={!!errors.target_audience}
                  helperText={errors.target_audience?.message}
                />
              </Grid>
              <Grid size={{ xs: 12, md: 6 }}>
                <DatePicker
                  fullWidth
                  label="Start Date"
                  value={
                    watch("start_date") ? dayjs(watch("start_date")) : null
                  }
                  onChange={(date) => {
                    setValue(
                      "start_date",
                      date ? dayjs(date).format("YYYY-MM-DD HH:mm:ss") : null,
                    );
                  }}
                />
              </Grid>
              <Grid size={{ xs: 12, md: 6 }}>
                <DatePicker
                  fullWidth
                  label="End Date"
                  value={watch("end_date") ? dayjs(watch("end_date")) : null}
                  onChange={(date) =>
                    setValue(
                      "end_date",
                      date ? dayjs(date).format("YYYY-MM-DD HH:mm:ss") : null,
                    )
                  }
                />
              </Grid>
              <Grid size={12}>
                <TextField
                  select
                  {...register("status")}
                  fullWidth
                  label="Status"
                  error={!!errors.status}
                  helperText={errors.status?.message}
                  value={watch("status") || "Active"}
                >
                  <MenuItem value="Active">Active</MenuItem>
                  <MenuItem value="Inactive">Inactive</MenuItem>
                </TextField>
              </Grid>
              <Grid size={12} display="flex" justifyContent="flex-end">
                <Button
                  type="submit"
                  variant="contained"
                  disabled={isLoading}
                  color="primary"
                  size="large"
                  startIcon={isLoading ? <CircularProgress size={20} /> : null}
                >
                  Create Ads
                </Button>
              </Grid>
            </Grid>
          </Box>
        </Form>
      </Card>

      <Snackbar
        open={successMessage || !!errorMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={errorMessage ? "error" : "success"}
          variant="filled"
        >
          {errorMessage || "Ads created successfully."}
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
}
