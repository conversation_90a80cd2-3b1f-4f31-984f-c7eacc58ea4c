import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import {
  Card,
  TextField,
  Box,
  CircularProgress,
  Typography,
  Paper,
  Chip,
  Avatar
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import { useGetAdsDetailMutation } from "../../services/adsApi";
import { useParams } from "react-router";
import { fDate, fTime } from "../../utils/format-time";

export default function AdsView() {
  const { id } = useParams();
  const [getAdsDetail, { data: adsData, isLoading }] =
    useGetAdsDetailMutation();

  const { watch, setValue } = useForm({});

  useEffect(() => {
    getAdsDetail({ id });
  }, [getAdsDetail, id]);

  useEffect(() => {
    if (adsData?.data) {
      Object.entries(adsData.data).forEach(([key, value]) => {
        setValue(key, value);
      });
    }
  }, [adsData, setValue]);

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Ads Details"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Ads", href: paths.main.ads.list },
          { name: "Ad Details" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: isLoading ? "center" : "flex-start",
        }}
      >
        {isLoading ? (
          <CircularProgress />
        ) : (
          <Form>
            <Box mt={3} width="100%">
              <Grid container spacing={2}>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Ad ID"
                    value={watch("id") || "----"}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Ad Title"
                    value={watch("ad_title") || "----"}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Advertiser Name"
                    value={watch("advertiser_name") || "----"}
                  />
                </Grid>
                <Grid size={6}>
                  <TextField
                    fullWidth
                    disabled
                    label="Ad Type"
                    value={watch("ad_type") || "----"}
                  />
                </Grid>
                <Grid size={6}>
                  <TextField
                    fullWidth
                    disabled
                    label="Ad Placement"
                    value={watch("ad_placement") || "----"}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Category"
                    value={watch("category") || "----"}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    fullWidth
                    disabled
                    label="Target Audience"
                    value={watch("target_audience") || "----"}
                  />
                </Grid>
                <Grid size={6}>
                  <TextField
                    fullWidth
                    disabled
                    label="Start Date"
                    value={
                      watch("start_date")
                        ? `${fDate(watch("start_date"))} ${fTime(watch("start_date"))}`
                        : "----"
                    }
                  />
                </Grid>
                <Grid size={6}>
                  <TextField
                    fullWidth
                    disabled
                    label="End Date"
                    value={
                      watch("end_date")
                        ? `${fDate(watch("end_date"))} ${fTime(watch("end_date"))}`
                        : "----"
                    }
                  />
                </Grid>
                <Grid size={6}>
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Status
                    </Typography>
                    <Chip
                      label={watch("status") || "----"}
                      color={getStatusColor(watch("status"))}
                      variant="filled"
                    />
                  </Box>
                </Grid>
                <Grid size={6}>
                  <TextField
                    fullWidth
                    disabled
                    label="Clicks"
                    value={watch("clicks") || "0"}
                  />
                </Grid>
                <Grid size={12}>
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Ad Image
                    </Typography>
                    {watch("ad_image") ? (
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 2,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          minHeight: 200,
                          backgroundColor: "grey.50",
                        }}
                      >
                        <img
                          src={watch("ad_image")}
                          alt={watch("ad_title") || "Ad Image"}
                          style={{
                            maxWidth: "100%",
                            maxHeight: "300px",
                            objectFit: "contain",
                          }}
                        />
                      </Paper>
                    ) : (
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 2,
                          minHeight: 100,
                          backgroundColor: "grey.50",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Typography color="text.secondary">
                          No image available
                        </Typography>
                      </Paper>
                    )}
                  </Box>
                </Grid>
                <Grid size={12}>
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Ad Media
                    </Typography>
                    {watch("ad_media") ? (
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 2,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          minHeight: 200,
                          backgroundColor: "grey.50",
                        }}
                      >
                        <video
                          controls
                          style={{
                            maxWidth: "100%",
                            maxHeight: "300px",
                          }}
                        >
                          <source src={watch("ad_media")} type="video/mp4" />
                          Your browser does not support the video tag.
                        </video>
                      </Paper>
                    ) : (
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 2,
                          minHeight: 100,
                          backgroundColor: "grey.50",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Typography color="text.secondary">
                          No media available
                        </Typography>
                      </Paper>
                    )}
                  </Box>
                </Grid>
                <Grid size={6}>
                  <TextField
                    fullWidth
                    disabled
                    label="Created At"
                    value={
                      watch("createdAt")
                        ? `${fDate(watch("createdAt"))} ${fTime(watch("createdAt"))}`
                        : "----"
                    }
                  />
                </Grid>
                <Grid size={6}>
                  <TextField
                    fullWidth
                    disabled
                    label="Updated At"
                    value={
                      watch("updatedAt")
                        ? `${fDate(watch("updatedAt"))} ${fTime(watch("updatedAt"))}`
                        : "----"
                    }
                  />
                </Grid>
              </Grid>
            </Box>
          </Form>
        )}
      </Card>
    </DashboardContent>
  );
}
