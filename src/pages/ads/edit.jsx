import { useEffect, useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { z as zod } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useParams } from "react-router";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import {
  Card,
  TextField,
  Button,
  Box,
  Snackbar,
  Alert,
  CircularProgress,
  MenuItem,
  Typography,
  Paper,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import {
  useEditAdsMutation,
  useGetAdsDetailMutation,
} from "../../services/adsApi";
import { useRouter } from "../../components/routes/hooks";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import InputFileUpload from "../../components/file-upload";
import { useSelector } from "react-redux";
import {
  adTypes,
  AdPlacements,
  MAX_IMAGE_SIZE,
  MAX_VIDEO_SIZE,
} from "../../utils/constants";
import { handleFileWithVideoCheck, uploadToS3 } from "../../utils/file-upload";

const EditAdsSchema = zod.object({
  ad_title: zod.string().min(1, "Ad title is required"),
  advertiser_name: zod.string().min(1, "Advertiser name is required"),
  ad_type: zod.string().min(1, "Ad type is required"),
  ad_placement: zod.string().min(1, "Ad placement is required"),
  category: zod.string().optional(),
  target_audience: zod.string().optional(),
  start_date: zod.string().min(1, "Start date is required"),
  end_date: zod.string().min(1, "End date is required"),
  status: zod.enum(["Active", "Inactive"], {
    message: "Invalid status selected.",
  }),
  ad_image: zod
    .any()
    .optional()
    .refine(
      (file) => {
        if (!file) return true; // Allow empty for edit
        return file.size <= MAX_IMAGE_SIZE;
      },
      {
        message: "Image must be less than 2MB",
      },
    )
    .refine(
      (file) => {
        if (!file) return true; // Allow empty for edit
        return (
          ["image/jpeg", "image/png", "image/gif"].includes(file.type) &&
          /\.(jpg|jpeg|png|gif)$/i.test(file.name)
        );
      },
      {
        message: "Only .jpg, .jpeg, .png, and .gif are allowed",
      },
    ),
  ad_media: zod
    .any()
    .optional()
    .refine(
      (file) => {
        if (!file) return true; // Allow empty for edit
        return file.size <= MAX_VIDEO_SIZE;
      },
      {
        message: "Video must be less than 20MB",
      },
    )
    .refine(
      (file) => {
        if (!file) return true; // Allow empty for edit
        return file.type === "video/mp4" && /\.mp4$/i.test(file.name);
      },
      {
        message: "Only .mp4 videos are allowed",
      },
    ),
});

export default function AdsEdit() {
  const { id } = useParams();
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState(false);

  const accessToken = useSelector((state) => state.auth.accessToken);

  const [editAds, { isLoading }] = useEditAdsMutation();
  const [getAdsDetail, { data: adsData, isLoading: isAdsDataLoading }] =
    useGetAdsDetailMutation();

  useEffect(() => {
    getAdsDetail({ id });
  }, [getAdsDetail, id]);

  const methods = useForm({
    resolver: zodResolver(EditAdsSchema),
    defaultValues: {
      ad_title: "",
      advertiser_name: "",
      ad_type: "",
      ad_image: null,
      ad_placement: "",
      category: "",
      target_audience: "",
      ad_media: null,
      start_date: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      end_date: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      status: "Active",
    },
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch,
    control,
  } = methods;

  useEffect(() => {
    if (adsData?.data) {
      const ads = adsData.data;
      reset({
        ad_title: ads.ad_title || "",
        advertiser_name: ads.advertiser_name || "",
        ad_type: ads.ad_type || "",
        ad_placement: ads.ad_placement || "",
        category: ads.category || "",
        target_audience: ads.target_audience || "",
        start_date: ads.start_date
          ? dayjs(ads.start_date).format("YYYY-MM-DD HH:mm:ss")
          : dayjs().format("YYYY-MM-DD HH:mm:ss"),
        end_date: ads.end_date
          ? dayjs(ads.end_date).format("YYYY-MM-DD HH:mm:ss")
          : dayjs().format("YYYY-MM-DD HH:mm:ss"),
        status: ads.status || "Active",
        ad_image: null, // Don't pre-populate files
        ad_media: null, // Don't pre-populate files
      });
    }
  }, [adsData, reset]);

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const onSubmit = async (data) => {
    console.log("Form submitted with data:", data);
    console.log("Form errors:", errors);
    try {
      let adImageUrl = adsData?.data?.ad_image;
      let adMediaURL = adsData?.data?.ad_media;

      // Upload new image if provided
      if (data.ad_image) {
        adImageUrl = await uploadToS3(
          data.ad_image,
          accessToken,
          "/media/generate-presigned-url",
        );
      }

      // Upload new media if provided
      if (data.ad_media) {
        adMediaURL = await uploadToS3(
          data.ad_media,
          accessToken,
          "/media/generate-presigned-url",
        );
      }

      const payload = {
        ...data,
        ad_image: adImageUrl,
        ad_media: adMediaURL,
      };

      await editAds({ id, ...payload }).unwrap();
      setSuccessMessage(true);
      setTimeout(() => {
        router.push("/ads");
      }, 1000);
    } catch (error) {
      console.error("Update Error:", error);
      setErrorMessage(error?.data?.message || "Failed to update ad.");
    }
  };

  const onInvalid = (errors) => {
    console.log("Form validation errors:", errors);
    alert("Form has validation errors. Check console.");
  };

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Edit Ad"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Ads", href: paths.main.ads.list },
          { name: "Edit" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: isAdsDataLoading ? "center" : "flex-start",
        }}
      >
        {isAdsDataLoading ? (
          <CircularProgress />
        ) : (
          <Form methods={methods} onSubmit={handleSubmit(onSubmit, onInvalid)}>
            <Box mt={3}>
              <Grid container spacing={2}>
                <Grid size={12}>
                  <TextField
                    {...register("ad_title")}
                    fullWidth
                    label="Ad Title"
                    error={!!errors.ad_title}
                    helperText={errors.ad_title?.message}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    {...register("advertiser_name")}
                    fullWidth
                    label="Advertiser Name"
                    error={!!errors.advertiser_name}
                    helperText={errors.advertiser_name?.message}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    select
                    {...register("ad_type")}
                    fullWidth
                    label="Ad Type"
                    error={!!errors.ad_type}
                    helperText={errors.ad_type?.message}
                    value={watch("ad_type") || ""}
                  >
                    {adTypes.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
                <Grid size={12}>
                  <TextField
                    select
                    {...register("ad_placement")}
                    fullWidth
                    label="Ad Placement"
                    error={!!errors.ad_placement}
                    helperText={errors.ad_placement?.message}
                    value={watch("ad_placement") || ""}
                  >
                    {AdPlacements.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
                <Grid size={12}>
                  <TextField
                    {...register("category")}
                    fullWidth
                    label="Category"
                    error={!!errors.category}
                    helperText={errors.category?.message}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    {...register("target_audience")}
                    fullWidth
                    label="Target Audience"
                    error={!!errors.target_audience}
                    helperText={errors.target_audience?.message}
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="start_date"
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <DatePicker
                        label="Start Date"
                        value={field.value ? dayjs(field.value) : null}
                        onChange={(date) => {
                          field.onChange(
                            date
                              ? dayjs(date).format("YYYY-MM-DD HH:mm:ss")
                              : "",
                          );
                        }}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            error: !!error,
                            helperText: error?.message,
                          },
                        }}
                      />
                    )}
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name="end_date"
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <DatePicker
                        label="End Date"
                        value={field.value ? dayjs(field.value) : null}
                        onChange={(date) => {
                          field.onChange(
                            date
                              ? dayjs(date).format("YYYY-MM-DD HH:mm:ss")
                              : "",
                          );
                        }}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            error: !!error,
                            helperText: error?.message,
                          },
                        }}
                      />
                    )}
                  />
                </Grid>

                {/* Current Ad Image Preview */}
                <Grid size={12}>
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Current Ad Image
                    </Typography>
                    {adsData?.data?.ad_image ? (
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 2,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          minHeight: 200,
                          backgroundColor: "grey.50",
                          mb: 2,
                        }}
                      >
                        <img
                          src={adsData.data.ad_image}
                          alt="Current Ad Image"
                          style={{
                            maxWidth: "100%",
                            maxHeight: "300px",
                            objectFit: "contain",
                          }}
                        />
                      </Paper>
                    ) : (
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 2,
                          minHeight: 100,
                          backgroundColor: "grey.50",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          mb: 2,
                        }}
                      >
                        <Typography color="text.secondary">
                          No current image
                        </Typography>
                      </Paper>
                    )}
                  </Box>
                </Grid>

                <Grid size={12}>
                  <InputFileUpload
                    name="ad_image"
                    label="Upload New Ad Image (Optional)"
                    value={watch("ad_image")}
                    onChange={(e) => setValue("ad_image", e.target.files?.[0])}
                    error={!!errors.ad_image}
                    helperText={
                      errors.ad_image?.message ||
                      "Leave empty to keep current image"
                    }
                  />
                </Grid>

                {/* Current Ad Media Preview */}
                <Grid size={12}>
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Current Ad Media
                    </Typography>
                    {adsData?.data?.ad_media ? (
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 2,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          minHeight: 200,
                          backgroundColor: "grey.50",
                          mb: 2,
                        }}
                      >
                        <video
                          controls
                          style={{
                            maxWidth: "100%",
                            maxHeight: "300px",
                          }}
                        >
                          <source
                            src={adsData.data.ad_media}
                            type="video/mp4"
                          />
                          Your browser does not support the video tag.
                        </video>
                      </Paper>
                    ) : (
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 2,
                          minHeight: 100,
                          backgroundColor: "grey.50",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          mb: 2,
                        }}
                      >
                        <Typography color="text.secondary">
                          No current media
                        </Typography>
                      </Paper>
                    )}
                  </Box>
                </Grid>

                <Grid size={12}>
                  <InputFileUpload
                    name="ad_media"
                    label="Upload New Ad Media (Optional)"
                    value={watch("ad_media")}
                    onChange={(e) =>
                      handleFileWithVideoCheck({
                        file: e.target.files?.[0],
                        maxDuration: 30,
                        setValue,
                        setErrorMessage,
                      })
                    }
                    error={!!errors.ad_media}
                    helperText={
                      errors.ad_media?.message ||
                      "Leave empty to keep current media"
                    }
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    select
                    {...register("status")}
                    fullWidth
                    label="Status"
                    error={!!errors.status}
                    helperText={errors.status?.message}
                    value={watch("status") || "Active"}
                  >
                    <MenuItem value="Active">Active</MenuItem>
                    <MenuItem value="Inactive">Inactive</MenuItem>
                  </TextField>
                </Grid>
                <Grid size={12} display="flex" justifyContent="flex-end">
                  <Button
                    type="submit"
                    variant="contained"
                    disabled={isLoading}
                    color="primary"
                    size="large"
                    startIcon={
                      isLoading ? <CircularProgress size={20} /> : null
                    }
                  >
                    Update Ad
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </Form>
        )}
      </Card>

      <Snackbar
        open={successMessage || !!errorMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={errorMessage ? "error" : "success"}
          variant="filled"
        >
          {errorMessage || "Ad updated successfully."}
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
}
