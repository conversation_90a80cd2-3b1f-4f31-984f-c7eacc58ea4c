import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z as zod } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useParams } from "react-router";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import {
  Card,
  TextField,
  Button,
  Box,
  Snackbar,
  Alert,
  CircularProgress,
  MenuItem,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Form } from "../../components/hook-form/form-provider";
import {
  useEditAdsMutation,
  useGetAdsDetailMutation,
} from "../../services/adsApi";
import { useRouter } from "../../components/routes/hooks";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import InputFileUpload from "../../components/file-upload";
import { useSelector } from "react-redux";
import {
  adTypes,
  AdPlacements,
  MAX_IMAGE_SIZE,
  MAX_VIDEO_SIZE,
} from "../../utils/constants";
import { handleFileWithVideoCheck, uploadToS3 } from "../../utils/file-upload";

const EditAdsSchema = zod.object({
  ad_title: zod.string().min(1, "Ad title is required"),
  advertiser_name: zod.string().min(1, "Advertiser name is required"),
  ad_type: zod.string().min(1, "Ad type is required"),
  ad_placement: zod.string().min(1, "Ad placement is required"),
  category: zod.string().optional(),
  target_audience: zod.string().optional(),
  start_date: zod.string().min(1, "Start date is required"),
  end_date: zod.string().min(1, "End date is required"),
  status: zod.enum(["Active", "Inactive"], {
    message: "Invalid status selected.",
  }),
  ad_image: zod
    .any()
    .optional()
    .refine((file) => !!file, {
      message: "Ad image is required",
    })
    .refine((file) => !!file && file.size <= MAX_IMAGE_SIZE, {
      message: "Image must be less than 2MB",
    })
    .refine(
      (file) =>
        file &&
        ["image/jpeg", "image/png", "image/gif"].includes(file.type) &&
        /\.(jpg|jpeg|png|gif)$/i.test(file.name),
      {
        message: "Only .jpg, .jpeg, .png, and .gif are allowed",
      },
    ),
  ad_media: zod
    .any()
    .optional()
    .refine((file) => !!file, {
      message: "Ad media is required",
    })
    .refine((file) => !!file && file.size <= MAX_VIDEO_SIZE, {
      message: "Video must be less than 20MB",
    })
    .refine(
      (file) => file && file.type === "video/mp4" && /\.mp4$/i.test(file.name),
      {
        message: "Only .mp4 videos are allowed",
      },
    ),
});

export default function UserEdit() {
  const { id } = useParams();
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState(false);

  const accessToken = useSelector((state) => state.auth.accessToken);

  const [editAds, { isLoading }] = useEditAdsMutation();
  const [getAdsDetail, { data: adsData, isLoading: isUserDataLoading }] =
    useGetAdsDetailMutation();

  useEffect(() => {
    getAdsDetail({ id });
    getRoleList();
  }, [getAdsDetail, getRoleList, id]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch,
  } = useForm({
    resolver: zodResolver(EditAdsSchema),
    defaultValues: {
      ad_title: "",
      advertiser_name: "",
      ad_type: "",
      ad_image: null,
      ad_placement: "",
      category: "",
      target_audience: "",
      ad_media: null,
      start_date: dayjs(),
      end_date: dayjs(),
      status: "Active",
    },
  });

  useEffect(() => {
    if (adsData?.data) {
      reset({
        ad_title: adsData.data.ad_title,
        advertiser_name: adsData.data.advertiser_name,
        ad_type: adsData.data.ad_type,
        ad_placement: adsData.data.ad_placement,
        category: adsData.data.category,
        target_audience: adsData.data.target_audience,
        start_date: dayjs(adsData.data.start_date),
        end_date: dayjs(adsData.data.end_date),
        status: adsData.data.status,
      });
    }
  }, [adsData, reset]);

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const onSubmit = async (data) => {
    try {
      if (!changePassword) {
        delete data.password;
        delete data.confirmPassword;
      }
      await editAds({ userId: id, ...data }).unwrap();
      setSuccessMessage(true);
      setTimeout(() => {
        router.push("/user");
      }, 1000);
    } catch (error) {
      console.error("Update Error:", error);
      setErrorMessage(error?.data?.message || "Failed to update user.");
    }
  };

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Edit User"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Admin Users", href: paths.main.user.list },
          { name: "Edit" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: isUserDataLoading ? "center" : "flex-start",
        }}
      >
        {isUserDataLoading ? (
          <CircularProgress />
        ) : (
          <Form
            methods={{ handleSubmit, register }}
            onSubmit={handleSubmit(onSubmit)}
          >
            <Box mt={3}>
              <Grid container spacing={2}>
                <Grid size={12}>
                  <TextField
                    {...register("first_name")}
                    fullWidth
                    label="First Name"
                    error={!!errors.first_name}
                    helperText={errors.first_name?.message}
                    value={watch("first_name") || ""}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    {...register("last_name")}
                    fullWidth
                    label="Last Name"
                    error={!!errors.last_name}
                    helperText={errors.last_name?.message}
                    value={watch("last_name") || ""}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    {...register("email")}
                    fullWidth
                    label="Email Address"
                    type="email"
                    disabled
                    error={!!errors.email}
                    helperText={errors.email?.message}
                    value={watch("email") || ""}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    {...register("mobile_number")}
                    fullWidth
                    label="Phone Number"
                    error={!!errors.mobile_number}
                    helperText={errors.mobile_number?.message}
                    value={watch("mobile_number") || ""}
                  />
                </Grid>
                <Grid size={12}>
                  <TextField
                    select
                    {...register("role")}
                    fullWidth
                    label="Role"
                    error={!!errors.role}
                    helperText={errors.role?.message}
                    value={watch("role") || ""}
                  >
                    {roleList?.data?.roles?.length ? (
                      roleList.data.roles.map((role) => (
                        <MenuItem key={role.id} value={role.id}>
                          {role.roleDisplayName}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem disabled>No roles available</MenuItem>
                    )}
                  </TextField>
                </Grid>
                <Grid size={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={watch("accountStatus") === "Active"}
                        onChange={(e) => {
                          const newStatus = e.target.checked
                            ? "Active"
                            : "Inactive";
                          setValue("accountStatus", newStatus, {
                            shouldValidate: true,
                            shouldDirty: true,
                          });
                        }}
                      />
                    }
                    label={`Account Status: ${watch("accountStatus") ?? "Inactive"}`}
                  />
                </Grid>

                <Grid size={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={changePassword}
                        onChange={() => setChangePassword(!changePassword)}
                      />
                    }
                    label="Change Password"
                  />
                </Grid>
                {changePassword && (
                  <>
                    <Grid size={12}>
                      <TextField
                        {...register("password")}
                        fullWidth
                        label="New Password"
                        type={showPassword.value ? "text" : "password"}
                        error={!!errors.password}
                        helperText={errors.password?.message}
                        slotProps={{
                          inputLabel: { shrink: true },
                          input: {
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton
                                  onClick={showPassword.onToggle}
                                  edge="end"
                                >
                                  <Iconify
                                    icon={
                                      showPassword.value
                                        ? "solar:eye-bold"
                                        : "solar:eye-closed-bold"
                                    }
                                  />
                                </IconButton>
                              </InputAdornment>
                            ),
                          },
                        }}
                      />
                    </Grid>
                    <Grid size={12}>
                      <TextField
                        {...register("confirmPassword")}
                        fullWidth
                        label="Confirm Password"
                        type={showConfirmPassword.value ? "text" : "password"}
                        error={!!errors.confirmPassword}
                        helperText={errors.confirmPassword?.message}
                        slotProps={{
                          inputLabel: { shrink: true },
                          input: {
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton
                                  onClick={showConfirmPassword.onToggle}
                                  edge="end"
                                >
                                  <Iconify
                                    icon={
                                      showConfirmPassword.value
                                        ? "solar:eye-bold"
                                        : "solar:eye-closed-bold"
                                    }
                                  />
                                </IconButton>
                              </InputAdornment>
                            ),
                          },
                        }}
                      />
                    </Grid>
                  </>
                )}
                <Grid size={12} display="flex" justifyContent="flex-end">
                  <Button
                    type="submit"
                    variant="contained"
                    disabled={isLoading}
                    color="primary"
                    size="large"
                    startIcon={
                      isLoading ? <CircularProgress size={20} /> : null
                    }
                  >
                    Update User
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </Form>
        )}
      </Card>

      <Snackbar
        open={successMessage || !!errorMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={errorMessage ? "error" : "success"}
          variant="filled"
        >
          {errorMessage || "User updated successfully."}
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
}
