import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Snackbar } from "@mui/material";
import { GridActionsCellItem } from "@mui/x-data-grid";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { ConfirmDialog } from "../../components/custom-dialog";
import { Iconify } from "../../components/iconify";
import { DashboardContent } from "../../layout/dashboard/content";
import { paths } from "../../routes/paths";
import { RouterLink } from "../../components/routes/components";
import {
  useDeleteAdsMutation,
  useGetAdsListMutation,
} from "../../services/adsApi";
import {
  GridActionsLinkItem,
  RenderDateCell,
  RenderImage,
  RenderStatus,
  RenderText,
} from "../../components/table/common";
import { TableData } from "../../components/table/table-content";

export default function AdsList() {
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [deleteId, setDeleteId] = useState("");
  const [tableData, setTableData] = useState([]);
  const [getAdsList, { data: ads, isLoading: adsLoading }] =
    useGetAdsListMutation();
  const [deleteAds] = useDeleteAdsMutation();

  useEffect(() => {
    getAdsList();
  }, []);

  useEffect(() => {
    if (ads?.data?.ads?.length) {
      const processedAds = ads.data.ads.map((ads) => ({
        ...ads,
        full_name: [ads.first_name, ads.last_name].filter(Boolean).join(" "),
      }));
      setTableData(processedAds);
    }
  }, [ads]);

  const handleDeleteRow = useCallback(async () => {
    try {
      await deleteAds({ id: deleteId }).unwrap();
      setSuccessMessage("Ads deleted successfully!");
      setDeleteId("");
      getAdsList();
    } catch (error) {
      setErrorMessage("Failed to delete Ads.");
    }
  }, [deleteAds, deleteId, getAdsList]);

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const columns = [
    {
      field: "id",
      headerName: "Ad ID",
      flex: 1,
      minWidth: 160,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.id} />,
    },
    {
      field: "ad_title",
      headerName: "Ad Title",
      flex: 1,
      minWidth: 160,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.ad_title} />,
    },
    {
      field: "ad_image",
      headerName: "Ad Image",
      flex: 1,
      minWidth: 160,
      disableColumnMenu: true,
      renderCell: (params) => (
        <RenderImage title={params.row.ad_title} image={params.row.ad_image} />
      ),
    },
    {
      field: "advertiser_name",
      headerName: "Advertiser Name",
      flex: 1,
      minWidth: 160,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.advertiser_name} />,
    },
    {
      field: "ad_type",
      headerName: "Ad Type",
      flex: 1,
      minWidth: 160,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText text={params.row.ad_type} />,
    },
    {
      field: "ad_placement",
      headerName: "Ad Placement",
      flex: 1,
      minWidth: 160,
      disableColumnMenu: true,
      renderCell: (params) => <RenderStatus status={params.row.ad_placement} />,
    },
    {
      field: "start_date",
      headerName: "Start Date",
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      renderCell: (params) => <RenderDateCell date={params.row.start_date} />,
    },
    {
      field: "end_date",
      headerName: "End Date",
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      renderCell: (params) => <RenderDateCell date={params.row.end_date} />,
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      minWidth: 110,
      disableColumnMenu: true,
      renderCell: (params) => <RenderStatus status={params.row.status} />,
    },
    {
      field: "clicks",
      headerName: "Clicks",
      flex: 1,
      minWidth: 110,
      disableColumnMenu: true,
      renderCell: (params) => <RenderText date={params.row.clicks} />,
    },
    {
      field: "createdAt",
      headerName: "Created At",
      flex: 1,
      minWidth: 200,
      disableColumnMenu: true,
      renderCell: (params) => <RenderDateCell date={params.row.createdAt} />,
    },
    {
      type: "actions",
      field: "actions",
      headerName: "Actions",
      flex: 1,
      minWidth: 80,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      getActions: (params) => [
        <GridActionsLinkItem
          showInMenu
          icon={<Iconify icon="solar:eye-bold" />}
          label="View"
          href={paths.main.ads.details(params.row.id)}
        />,
        <GridActionsLinkItem
          showInMenu
          icon={<Iconify icon="solar:pen-bold" />}
          label="Edit"
          href={paths.main.ads.edit(params.row.id)}
        />,
        <GridActionsCellItem
          showInMenu
          icon={<Iconify icon="solar:trash-bin-trash-bold" />}
          label="Delete"
          onClick={() => setDeleteId(params.row.id)}
          sx={{ color: "error.main" }}
        />,
      ],
    },
  ];

  const renderConfirmDialog = () => (
    <ConfirmDialog
      open={!!deleteId}
      onClose={() => setDeleteId("")}
      title="Delete"
      content={<> Are you sure want to delete ? </>}
      action={
        <Button
          variant="contained"
          color="error"
          onClick={() => {
            handleDeleteRow();
          }}
        >
          Delete
        </Button>
      }
    />
  );

  return (
    <>
      <DashboardContent
        sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}
      >
        <CustomBreadcrumbs
          heading="Ads"
          links={[
            { name: "Dashboard", href: paths.main.root },
            { name: "Ads" },
          ]}
          action={
            <Button
              component={RouterLink}
              href={paths.main.ads.new}
              variant="contained"
              startIcon={<Iconify icon="mingcute:add-line" />}
            >
              New Ad
            </Button>
          }
          sx={{ mb: { xs: 3, md: 5 } }}
        />

        <Card
          sx={{
            flexGrow: { md: 1 },
            display: { md: "flex" },
            height: { xs: 800, md: "1px" },
            flexDirection: { md: "column" },
          }}
        >
          <TableData
            tableData={tableData}
            columns={columns}
            productsLoading={adsLoading}
          />
        </Card>

        {/* Snackbar Notification */}
        <Snackbar
          open={!!successMessage || !!errorMessage}
          autoHideDuration={3000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "top", horizontal: "right" }} // Position of Snackbar
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={errorMessage ? "error" : "success"}
            variant="filled"
          >
            {successMessage || errorMessage}
          </Alert>
        </Snackbar>
      </DashboardContent>

      {renderConfirmDialog()}
    </>
  );
}
