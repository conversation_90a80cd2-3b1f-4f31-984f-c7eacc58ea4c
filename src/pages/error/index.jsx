import { Box, Button, Container, Typography } from "@mui/material";
import PageNotFound from "../../assets/not-found";
import { RouterLink } from "../../components/routes/components";
import { LayoutSection } from "../../layout/core/layout-section";

export default function Page404() {
  const renderMain = () => (
    <Box
      sx={[
        (theme) => ({
          width: 1,
          mx: "auto",
          display: "flex",
          flex: "1 1 auto",
          textAlign: "center",
          flexDirection: "column",
          p: theme.spacing(3, 2, 10, 2),
          maxWidth: "448px",
          justifyContent: "center",
        }),
      ]}
    >
      <Container>
        <Typography variant="h3" sx={{ mb: 2 }}>
          Sorry, page not found!
        </Typography>

        <Typography sx={{ color: "text.secondary" }}>
          Sorry, we couldn’t find the page you’re looking for. Perhaps you’ve
          mistyped the URL? Be sure to check your spelling.
        </Typography>

        <PageNotFound sx={{ my: { xs: 5, sm: 10 } }} />

        <Button
          component={RouterLink}
          href="/"
          size="large"
          variant="contained"
        >
          Go to home
        </Button>
      </Container>
    </Box>
  );
  return <LayoutSection>{renderMain()}</LayoutSection>;
}
