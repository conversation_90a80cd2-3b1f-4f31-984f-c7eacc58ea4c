import React, { useState } from "react";
import {
  Card,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  Typography,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import { DashboardContent } from "../../layout/dashboard/content";
import { paths } from "../../routes/paths";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { Iconify } from "../../components/iconify";

const notifications = [
  {
    id: 1,
    title: "New Feature Update",
    content: "We have added a new feature!",
    audience: "All Users",
    type: "Push",
    status: "Sent",
    createdBy: "Admin",
    createdDate: "2024-03-24",
  },
  {
    id: 2,
    title: "Maintenance Notice",
    content: "Scheduled maintenance on 25th March.",
    audience: "Role-Based",
    type: "In-App",
    status: "Scheduled",
    createdBy: "Admin",
    createdDate: "2024-03-23",
  },
];

export default function NotificationList() {
  const navigate = useNavigate();
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedId, setSelectedId] = useState(null);

  const handleView = (id) => navigate(`/notification/view/${id}`);
  const handleEdit = (id) => navigate(`/notification/edit/${id}`);

  const handleDelete = (id) => {
    setSelectedId(id);
    setOpenDeleteDialog(true);
  };

  const confirmDelete = () => {
    console.log("Deleting notification with ID:", selectedId);
    setOpenDeleteDialog(false);
  };

  return (
    <DashboardContent
      sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}
    >
      <CustomBreadcrumbs
        heading="Notifications"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Notifications" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />
      <Card sx={{ p: 3, boxShadow: 3, borderRadius: 2 }}>
        <Typography variant="h5" fontWeight="bold" gutterBottom>
          Notifications
        </Typography>

        <TableContainer sx={{ overflowX: "auto" }}>
          <Table sx={{ minWidth: 1200 }}>
            {" "}
            {/* adjust minWidth as needed */}
            <TableHead>
              <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
                {[
                  "Title",
                  "Message Content",
                  "Target Audience",
                  "Notification Type",
                  "Status",
                  "Scheduled Time",
                  "Created By",
                  "Created Date",
                  "Actions",
                ].map((header) => (
                  <TableCell key={header} sx={{ whiteSpace: "nowrap" }}>
                    <strong>{header}</strong>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {notifications.map((notification) => (
                <TableRow key={notification.id} hover>
                  <TableCell sx={{ whiteSpace: "nowrap" }}>
                    {notification.title}
                  </TableCell>
                  <TableCell sx={{ whiteSpace: "nowrap" }}>
                    {notification.content}
                  </TableCell>
                  <TableCell sx={{ whiteSpace: "nowrap" }}>
                    {notification.audience}
                  </TableCell>
                  <TableCell sx={{ whiteSpace: "nowrap" }}>
                    {notification.type}
                  </TableCell>
                  <TableCell sx={{ whiteSpace: "nowrap" }}>
                    {notification.status}
                  </TableCell>
                  <TableCell sx={{ whiteSpace: "nowrap" }}>
                    {notification.scheduledTime || "N/A"}
                  </TableCell>
                  <TableCell sx={{ whiteSpace: "nowrap" }}>
                    {notification.createdBy}
                  </TableCell>
                  <TableCell sx={{ whiteSpace: "nowrap" }}>
                    {notification.createdDate}
                  </TableCell>
                  <TableCell sx={{ whiteSpace: "nowrap" }}>
                    <IconButton onClick={() => handleView(notification.id)}>
                      <Iconify icon="solar:eye-bold" />
                    </IconButton>
                    <IconButton onClick={() => handleEdit(notification.id)}>
                      <Iconify icon="solar:pen-bold" />
                    </IconButton>
                    <IconButton onClick={() => handleDelete(notification.id)}>
                      <Iconify icon="solar:trash-bin-trash-bold" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={openDeleteDialog}
          onClose={() => setOpenDeleteDialog(false)}
        >
          <DialogTitle sx={{ fontWeight: "bold" }}>
            Confirm Deletion
          </DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete this notification? This action
              cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setOpenDeleteDialog(false)}
              variant="outlined"
            >
              Cancel
            </Button>
            <Button onClick={confirmDelete} color="error" variant="contained">
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </Card>
    </DashboardContent>
  );
}
