import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Di<PERSON><PERSON>, Box } from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { DashboardContent } from "../../layout/dashboard/content";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { paths } from "../../routes/paths";

const notifications = [
  {
    id: 1,
    title: "New Feature Update",
    content: "We have added a new feature!",
    audience: "All Users",
    type: "Push",
    status: "Sent",
    scheduledTime: "2024-03-25 10:00 AM",
    sentTime: "2024-03-25 10:05 AM",
    createdBy: "Admin",
    createdDate: "2024-03-24",
  },
];

export default function ViewNotification() {
  const { id } = useParams();
  const navigate = useNavigate();
  const notification = notifications.find((n) => n.id === parseInt(id));

  if (!notification)
    return (
      <Typography sx={{ textAlign: "center", mt: 4 }}>
        Notification not found!
      </Typography>
    );

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="View Notification"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Notifications", href: paths.main.notification },
          { name: "View Notification" },
        ]}
        sx={{ mb: { xs: 3, md: 5 }, ml: { xs: 2, md: 4 } }}
      />

      <Card
        sx={{
          p: 4,
          maxWidth: 900,
          boxShadow: 3,
          borderRadius: 2,
          bgcolor: "background.paper",
        }}
      >
        <Typography variant="h5" fontWeight="bold" sx={{ mb: 2 }}>
          {notification.title}
        </Typography>

        <Divider sx={{ mb: 3 }} />

        <Box sx={{ mb: 2 }}>
          <Typography variant="body1" color="text.secondary">
            <strong>Message:</strong>
          </Typography>
          <Typography variant="body1" sx={{ mt: 0.5 }}>
            {notification.content}
          </Typography>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="body1" color="text.secondary">
            <strong>Target Audience:</strong>
          </Typography>
          <Typography variant="body1" sx={{ mt: 0.5 }}>
            {notification.audience}
          </Typography>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="body1" color="text.secondary">
            <strong>Notification Type:</strong>
          </Typography>
          <Typography variant="body1" sx={{ mt: 0.5 }}>
            {notification.type}
          </Typography>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="body1" color="text.secondary">
            <strong>Status:</strong>
          </Typography>
          <Typography
            variant="body1"
            sx={{
              mt: 0.5,
              color: notification.status === "Sent" ? "green" : "orange",
            }}
          >
            {notification.status}
          </Typography>
        </Box>

        {notification.scheduledTime && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body1" color="text.secondary">
              <strong>Scheduled Time:</strong>
            </Typography>
            <Typography variant="body1" sx={{ mt: 0.5 }}>
              {notification.scheduledTime}
            </Typography>
          </Box>
        )}

        {notification.sentTime && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body1" color="text.secondary">
              <strong>Sent Time:</strong>
            </Typography>
            <Typography variant="body1" sx={{ mt: 0.5 }}>
              {notification.sentTime}
            </Typography>
          </Box>
        )}

        <Divider sx={{ my: 3 }} />

        <Box sx={{ mb: 2 }}>
          <Typography variant="body1" color="text.secondary">
            <strong>Created By:</strong>
          </Typography>
          <Typography variant="body1" sx={{ mt: 0.5 }}>
            {notification.createdBy}
          </Typography>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="body1" color="text.secondary">
            <strong>Created Date:</strong>
          </Typography>
          <Typography variant="body1" sx={{ mt: 0.5 }}>
            {notification.createdDate}
          </Typography>
        </Box>

        <Button onClick={() => navigate(-1)} variant="contained">
          Back
        </Button>
      </Card>
    </DashboardContent>
  );
}
