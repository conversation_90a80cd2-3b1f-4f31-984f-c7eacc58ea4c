import React, { useState } from "react";
import {
  Card,
  TextField,
  Button,
  Typography,
  MenuItem,
  Switch,
  FormControlLabel,
} from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { DashboardContent } from "../../layout/dashboard/content";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { paths } from "../../routes/paths";

const notifications = [
  {
    id: 1,
    title: "New Feature Update",
    content: "We have added a new feature!",
    audience: "All Users",
    type: "Push",
    status: "Sent",
    scheduledTime: "2024-03-25 10:00 AM",
    createdBy: "Admin",
    createdDate: "2024-03-24",
  },
];

export default function EditNotification() {
  const { id } = useParams();
  const navigate = useNavigate();
  const notification = notifications.find((n) => n.id === parseInt(id));

  const [formData, setFormData] = useState(
    notification || {
      title: "",
      content: "",
      audience: "All Users",
      type: "In-App",
      schedule: false,
      scheduledTime: "",
      status: "Draft",
    },
  );

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleScheduleToggle = (e) => {
    setFormData({ ...formData, schedule: e.target.checked, scheduledTime: "" });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Updated Notification:", formData);
    navigate("/notifications");
  };

  if (!notification)
    return (
      <Typography sx={{ textAlign: "center", mt: 4 }}>
        Notification not found!
      </Typography>
    );

  return (
    <DashboardContent>
      {/* Breadcrumbs */}
      <CustomBreadcrumbs
        heading="Edit Notification"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Notifications", href: paths.main.notification },
          { name: "Edit Notification" },
        ]}
        sx={{ mb: { xs: 3, md: 5 }, ml: { xs: 2, md: 4 } }}
      />

      <Card sx={{ p: 3, maxWidth: 900, boxShadow: 3, borderRadius: 2 }}>
        <Typography variant="h5" fontWeight="bold">
          Edit Notification
        </Typography>

        <form onSubmit={handleSubmit}>
          <TextField
            fullWidth
            label="Title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            sx={{ mt: 2 }}
            required
            inputProps={{ maxLength: 100 }}
          />

          <TextField
            fullWidth
            label="Message Content"
            name="content"
            value={formData.content}
            onChange={handleChange}
            sx={{ mt: 2 }}
            required
            multiline
            rows={3}
            inputProps={{ maxLength: 500 }}
          />

          {/* Target Audience */}
          <TextField
            select
            fullWidth
            label="Target Audience"
            name="audience"
            value={formData.audience}
            onChange={handleChange}
            sx={{ mt: 2 }}
            required
          >
            <MenuItem value="All Users">All Users</MenuItem>
            <MenuItem value="Specific Users">Specific Users</MenuItem>
            <MenuItem value="Role-Based">Role-Based</MenuItem>
          </TextField>

          {/* Notification Type */}
          <TextField
            select
            fullWidth
            label="Notification Type"
            name="type"
            value={formData.type}
            onChange={handleChange}
            sx={{ mt: 2 }}
            required
          >
            <MenuItem value="In-App">In-App</MenuItem>
            <MenuItem value="Push">Push</MenuItem>
          </TextField>

          {/* Schedule Toggle */}
          <FormControlLabel
            control={
              <Switch
                checked={formData.schedule}
                onChange={handleScheduleToggle}
              />
            }
            label="Schedule Notification?"
            sx={{ mt: 2 }}
          />

          {/* Scheduled Time (Only if schedule is enabled) */}
          {formData.schedule && (
            <TextField
              fullWidth
              type="datetime-local"
              name="scheduledTime"
              value={formData.scheduledTime}
              onChange={handleChange}
              sx={{ mt: 2 }}
              required
              inputProps={{ min: new Date().toISOString().slice(0, 16) }} // Ensure only future dates
            />
          )}

          {/* Status */}
          <TextField
            select
            fullWidth
            label="Status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            sx={{ mt: 2 }}
            required
          >
            <MenuItem value="Draft">Draft</MenuItem>
            <MenuItem value="Scheduled">Scheduled</MenuItem>
            <MenuItem value="Sent">Sent</MenuItem>
          </TextField>

          <Button type="submit" variant="contained" sx={{ mt: 3 }}>
            Update Notification
          </Button>
        </form>
      </Card>
    </DashboardContent>
  );
}
