import {
  Card,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useTheme } from "@mui/material/styles";
import { useState, useMemo } from "react";
import { DashboardContent } from "../../layout/dashboard/content";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { paths } from "../../routes/paths";
import { Bar<PERSON>hart, LineChart } from "@mui/x-charts";
import { AppWidgetSummary } from "../../components/app-widget-sumary/app-widget-summary";
import { useGetUserAnalyticsMutation } from "../../services/userAnalyticsApi";
import { useEffect } from "react";

export default function UserAnalytics() {
  const theme = useTheme();
  const [timeRange, setTimeRange] = useState("currentMonth");

  const [getUserAnalytics, { data: analytics, isLoading, isError }] =
    useGetUserAnalyticsMutation();

  useEffect(() => {
    getUserAnalytics({ period: timeRange });
  }, [timeRange]);

  const metrics = useMemo(() => {
    if (!analytics) return [];
    return [
      {
        title: "Total Time Spent",
        total: analytics.totalTimeSpent,
        color: theme.palette.success.main,
      },
      {
        title: "Total Active Users",
        total: analytics.totalActiveUsers,
        color: theme.palette.info.main,
      },
      {
        title: "Total Installed",
        total: analytics.totalInstalled,
        color: theme.palette.primary.main,
      },
      {
        title: "Total Downloads",
        total: analytics.totalDownloads,
        color: theme.palette.secondary.main,
      },
    ];
  }, [analytics, theme.palette]);

  if (isLoading) return <Typography>Loading...</Typography>;
  if (isError || !analytics)
    return <Typography>Error loading data.</Typography>;

  return (
    <DashboardContent
      sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}
    >
      <CustomBreadcrumbs
        heading="User Analytics"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "User Analytics" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      {/* Time Range Selector */}
      <Box display="flex" justifyContent="flex-end" mb={3}>
        <FormControl sx={{ width: 200 }}>
          <InputLabel>Time Range</InputLabel>
          <Select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            label="Time Range"
          >
            <MenuItem value="currentMonth">Current Month</MenuItem>
            <MenuItem value="last3Months">Last 3 Months</MenuItem>
            <MenuItem value="last6Months">Last 6 Months</MenuItem>
            <MenuItem value="last12Months">Last 12 Months</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Grid container spacing={4}>
        {metrics.map((item, idx) => (
          <Grid key={idx} size={{ xs: 12, md: 6 }}>
            <AppWidgetSummary
              title={item.title}
              total={item.total}
              percent={5}
              chart={{
                categories: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                colors: [item.color],
                series: [60, 25, 15],
              }}
            />
          </Grid>
        ))}

        {/* Downloads Over Time */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" mb={2}>
              Downloads Over Time
            </Typography>
            <Box sx={{ width: "100%", height: 300 }}>
              {analytics.downloadStats?.length > 0 && (
                <LineChart
                  height={300}
                  series={[
                    {
                      data: analytics.downloadStats,
                      color: theme.palette.secondary.main,
                    },
                  ]}
                  xAxis={[
                    {
                      scaleType: "point",
                      data: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                    },
                  ]}
                />
              )}
            </Box>
          </Card>
        </Grid>

        {/* User Growth */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" mb={2}>
              User Growth Over Time
            </Typography>
            <Box sx={{ width: "100%", height: 300 }}>
              {analytics.userGrowth?.length > 0 && (
                <LineChart
                  height={300}
                  series={[
                    {
                      data: analytics.userGrowth,
                      color: theme.palette.success.main,
                    },
                  ]}
                  xAxis={[
                    {
                      scaleType: "point",
                      data: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                    },
                  ]}
                />
              )}
            </Box>
          </Card>
        </Grid>

        {/* Top Installed Locations */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" mb={2}>
              Top Installed Locations
            </Typography>
            <List sx={{ maxHeight: 340, overflowY: "auto" }}>
              {analytics.topLocations?.map((loc, index) => (
                <ListItem key={index}>
                  <Box sx={{ width: 32, height: 24, mr: 2 }}>
                    <img
                      src={`https://flagcdn.com/w40/${loc.countryCode}.png`}
                      alt={`${loc.countryCode} flag`}
                      width="100%"
                      height="100%"
                      style={{ objectFit: "cover", borderRadius: 4 }}
                    />
                  </Box>
                  <ListItemText
                    primary={loc.city}
                    secondary={`Installs: ${loc.installs}`}
                  />
                </ListItem>
              ))}
            </List>
          </Card>
        </Grid>

        {/* Top Users */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" mb={2}>
              Top 10 Users by Hours Spent
            </Typography>
            <Box sx={{ maxHeight: 320, overflowY: "auto" }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell>User Name</TableCell>
                    <TableCell>Hours Spent</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {analytics.topUsers?.map((user, index) => (
                    <TableRow key={index}>
                      <TableCell>{user.name}</TableCell>
                      <TableCell>{user.hours}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>
          </Card>
        </Grid>

        {/* Revenue Chart */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" mb={2} textAlign="center">
              Revenue (Month-wise)
            </Typography>
            <Box sx={{ width: "100%", height: 300 }}>
              {analytics.revenue?.length > 0 && (
                <BarChart
                  xAxis={[
                    {
                      scaleType: "band",
                      data: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                    },
                  ]}
                  series={[
                    {
                      data: analytics.revenue,
                      color: theme.palette.primary.main,
                    },
                  ]}
                />
              )}
            </Box>
          </Card>
        </Grid>

        {/* Engagement by Feature */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" mb={2}>
              User Engagement by Feature
            </Typography>
            <Table sx={{ maxHeight: 340, overflowY: "auto" }}>
              <TableHead>
                <TableRow>
                  <TableCell>Feature</TableCell>
                  <TableCell>Users Engaged</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {analytics.engagementData?.map((row, index) => (
                  <TableRow key={index}>
                    <TableCell>{row.feature}</TableCell>
                    <TableCell>{row.users}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>
        </Grid>
      </Grid>
    </DashboardContent>
  );
}
