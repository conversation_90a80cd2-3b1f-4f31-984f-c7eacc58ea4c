import { useState } from "react";
import { useForm } from "react-hook-form";
import { z as zod } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Card,
  TextField,
  Button,
  Box,
  Snackbar,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { DashboardContent } from "../../layout/dashboard/content";
import { Form } from "../../components/hook-form/form-provider";
import { FormHead } from "../../components/auth/form-head";
import { useChangePasswordMutation } from "../../services/userApi";
import { useBoolean } from "minimal-shared/hooks";
import { Iconify } from "../../components/iconify";

const ChangePasswordSchema = zod
  .object({
    oldPassword: zod.string().min(1, "Old password is required."),
    newPassword: zod
      .string()
      .min(8, "New password must be at least 8 characters.")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter!")
      .regex(/\d/, "Password must contain at least one number!")
      .regex(
        /[@$!%*?&]/,
        "Password must contain at least one special character!",
      ),
    confirmNewPassword: zod.string(),
  })
  .refine((data) => data.newPassword === data.confirmNewPassword, {
    message: "Passwords must match!",
    path: ["confirmNewPassword"],
  });

export default function ChangePassword() {
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState(false);
  const [changePassword, { isLoading }] = useChangePasswordMutation();
  const showOldPassword = useBoolean();
  const showNewPassword = useBoolean();
  const showConfirmPassword = useBoolean();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(ChangePasswordSchema),
    defaultValues: {
      oldPassword: "",
      newPassword: "",
      confirmNewPassword: "",
    },
  });

  const handleCloseSnackbar = () => {
    setSuccessMessage(false);
    setErrorMessage("");
  };

  const onSubmit = async (data) => {
    try {
      await changePassword(data).unwrap();
      setSuccessMessage(true);
      reset();
    } catch (error) {
      setErrorMessage(error.data?.message || "Failed to change password.");
    }
  };

  return (
    <DashboardContent
      sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}
    >
      <Card
        sx={{
          minHeight: 400,
          maxWidth: 900,
          p: 4,
          boxShadow: 3,
          borderRadius: 2,
        }}
      >
        <FormHead
          title="Change Password"
          description="Enter your current and new password"
        />

        <Form
          methods={{ handleSubmit, register }}
          onSubmit={handleSubmit(onSubmit)}
        >
          <Box mt={3}>
            <Grid container spacing={2}>
              <Grid size={12}>
                <TextField
                  {...register("oldPassword")}
                  fullWidth
                  label="Old Password"
                  type={showOldPassword.value ? "text" : "password"}
                  error={!!errors.oldPassword}
                  helperText={errors.oldPassword?.message}
                  slotProps={{
                    inputLabel: { shrink: true },
                    input: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={showOldPassword.onToggle}
                            edge="end"
                          >
                            <Iconify
                              icon={
                                showOldPassword.value
                                  ? "solar:eye-bold"
                                  : "solar:eye-closed-bold"
                              }
                            />
                          </IconButton>
                        </InputAdornment>
                      ),
                    },
                  }}
                />
              </Grid>
              <Grid size={12}>
                <TextField
                  {...register("newPassword")}
                  fullWidth
                  label="New Password"
                  type={showNewPassword.value ? "text" : "password"}
                  error={!!errors.newPassword}
                  helperText={errors.newPassword?.message}
                  slotProps={{
                    inputLabel: { shrink: true },
                    input: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={showNewPassword.onToggle}
                            edge="end"
                          >
                            <Iconify
                              icon={
                                showNewPassword.value
                                  ? "solar:eye-bold"
                                  : "solar:eye-closed-bold"
                              }
                            />
                          </IconButton>
                        </InputAdornment>
                      ),
                    },
                  }}
                />
              </Grid>
              <Grid size={12}>
                <TextField
                  {...register("confirmNewPassword")}
                  fullWidth
                  label="Confirm New Password"
                  type={showConfirmPassword.value ? "text" : "password"}
                  error={!!errors.confirmNewPassword}
                  helperText={errors.confirmNewPassword?.message}
                  slotProps={{
                    inputLabel: { shrink: true },
                    input: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={showConfirmPassword.onToggle}
                            edge="end"
                          >
                            <Iconify
                              icon={
                                showConfirmPassword.value
                                  ? "solar:eye-bold"
                                  : "solar:eye-closed-bold"
                              }
                            />
                          </IconButton>
                        </InputAdornment>
                      ),
                    },
                  }}
                />
              </Grid>
              <Grid size={12} display="flex" justifyContent="flex-end">
                <Button
                  type="submit"
                  variant="contained"
                  disabled={isLoading}
                  color="primary"
                  size="large"
                  startIcon={isLoading ? <CircularProgress size={20} /> : null}
                >
                  Change Password
                </Button>
              </Grid>
            </Grid>
          </Box>
        </Form>
      </Card>

      {/* Snackbar Notification */}
      <Snackbar
        open={successMessage || !!errorMessage}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "top", horizontal: "right" }} // Position of Snackbar
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={errorMessage ? "error" : "success"}
          variant="filled"
        >
          {errorMessage || "Password changed successfully."}
        </Alert>
      </Snackbar>
    </DashboardContent>
  );
}
