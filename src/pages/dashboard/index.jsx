import React, { useEffect, useState } from "react";
import { useTheme } from "@mui/material/styles";
import {
  Card,
  Grid,
  Typography,
  Box,
  Stack,
  Button,
  Select,
  MenuItem,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
} from "@mui/material";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mui/x-charts";
import ArrowDropUpIcon from "@mui/icons-material/ArrowDropUp";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { DashboardContent } from "../../layout/dashboard/content";
import { paths } from "../../routes/paths";
import {
  useGetDashboardDataMutation,
  useGetDashboardPostDetailsMutation,
} from "../../services/dashboardApi";
import { Modal, Fade, Backdrop, Paper, IconButton } from "@mui/material";
import { TableContainer } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

export default function UserStatsOverview() {
  const theme = useTheme();

  const [filter, setFilter] = useState("all");
  const [dashboardView, setDashboardView] = useState({
    statsData: [],
    contentStats: [],
    topPosts: [],
    donationStats: [],
    topCampaigns: [],
    revenueTrend: [],
    revenueLabels: [],
    liveStats: [],
  });

  const [openModal, setOpenModal] = useState(null);

  const handleOpen = (modalName) => setOpenModal(modalName);
  const handleClose = () => setOpenModal(null);

  const [getDashboardData, { isLoading, isError }] =
    useGetDashboardDataMutation();

  useEffect(() => {
    getDashboardData()
      .then((res) => {
        const d = res?.data?.data;

        if (
          !d ||
          !d.users ||
          !d.activeUsers ||
          !d.inactiveUsers ||
          !d.donations ||
          !d.goLive
        ) {
          console.error("Incomplete dashboard data:", res);
          return;
        }

        const mappedStatsData = [
          {
            title: "Users",
            value: d.users.count,
            change: d.users.percentageChange,
            chartData: [],
          },
          {
            title: "Active Users",
            value: { today: d.activeUsers.count, month: d.activeUsers.count },
            change: d.activeUsers.percentageChange,
            chartData: [],
          },
          {
            title: "Inactive Users",
            value: d.inactiveUsers.count,
            change: d.inactiveUsers.percentageChange,
            chartData: [],
          },
        ];

        const mappedLiveStats = [
          { title: "Total Sessions", value: d.goLive.totalSessions },
          { title: "Ongoing Lives", value: d.goLive.ongoingLives },
          { title: "Avg View Duration", value: d.goLive.avergaeViewDurations },
          { title: "Peak Viewers", value: d.goLive.peakViewers },
        ];

        const mappedDonationStats = [
          { title: "Total Donations", value: d.donations.totalDonations },
          { title: "Total Donors", value: d.donations.totalDonors },
          { title: "Average Donation", value: d.donations.averageDonations },
        ];

        const mappedRevenueTrend =
          d.donations.lineChartData?.map((item) => item.amount) || [];
        const mappedRevenueLabels =
          d.donations.lineChartData?.map((item) => item.month) || [];

        const mappedTopCampaigns =
          d.donations.topCampaigns?.map((c) => ({
            name: c.name,
            amount: c.amountRaised,
          })) || [];

        setDashboardView({
          statsData: mappedStatsData,
          contentStats: [],
          topPosts: [],
          donationStats: mappedDonationStats,
          topCampaigns: mappedTopCampaigns,
          revenueTrend: mappedRevenueTrend,
          revenueLabels: mappedRevenueLabels,
          liveStats: mappedLiveStats,
        });
      })
      .catch((error) => {
        console.error("Error fetching dashboard data:", error);
      });
  }, []);

  const donationChartData = [
    { name: "W1", donations: 1200 },
    { name: "W2", donations: 1800 },
    { name: "W3", donations: 1500 },
    { name: "W4", donations: 2000 },
  ];

  const {
    statsData,
    contentStats,
    topPosts,
    donationStats,
    topCampaigns,
    revenueTrend,
    revenueLabels,
    liveStats,
  } = dashboardView;

  useEffect(() => {
    getDashboardData().then((res) => {
      const d = res?.data?.data;

      if (
        !d ||
        !d.users ||
        !d.activeUsers ||
        !d.inactiveUsers ||
        !d.donations ||
        !d.goLive
      ) {
        console.error("Incomplete dashboard data:", res);
        return;
      }

      const mappedStatsData = [
        {
          title: "Users",
          value: d.users.count,
          change: d.users.percentageChange,
          chartData: [],
        },
        {
          title: "Active Users",
          value: { today: d.activeUsers.count, month: d.activeUsers.count },
          change: d.activeUsers.percentageChange,
          chartData: [],
        },
        {
          title: "Inactive Users",
          value: d.inactiveUsers.count,
          change: d.inactiveUsers.percentageChange,
          chartData: [],
        },
      ];

      const mappedLiveStats = [
        { title: "Total Sessions", value: d.goLive.totalSessions },
        { title: "Ongoing Lives", value: d.goLive.ongoingLives },
        { title: "Avg View Duration", value: d.goLive.avergaeViewDurations },
        { title: "Peak Viewers", value: d.goLive.peakViewers },
      ];

      const mappedDonationStats = [
        { title: "Total Donations", value: d.donations.totalDonations },
        { title: "Total Donors", value: d.donations.totalDonors },
        { title: "Average Donation", value: d.donations.averageDonations },
      ];

      const mappedRevenueTrend =
        d.donations.lineChartData?.map((item) => item.amount) || [];
      const mappedRevenueLabels =
        d.donations.lineChartData?.map((item) => item.month) || [];

      const mappedTopCampaigns =
        d.donations.topCampaigns?.map((c) => ({
          name: c.name,
          amount: c.amountRaised,
        })) || [];

      setDashboardView({
        statsData: mappedStatsData,
        contentStats: [],
        topPosts: [],
        donationStats: mappedDonationStats,
        topCampaigns: mappedTopCampaigns,
        revenueTrend: mappedRevenueTrend,
        revenueLabels: mappedRevenueLabels,
        liveStats: mappedLiveStats,
      });
    });
  }, []);

  if (isLoading) return <Typography>Loading...</Typography>;
  if (isError) return <Typography>Error fetching data</Typography>;

  const barColors = ["green", "green", "green", "green"];

  return (
    <DashboardContent maxWidth="xl">
      <CustomBreadcrumbs
        heading="User Summary"
        links={[{ name: "Dashboard", href: paths.main.root }, { name: "Home" }]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      {/* Stats Data Cards */}
      <Grid container spacing={3}>
        {statsData.map((stat, idx) => {
          const isPositive = stat.change >= 0;
          const chartColor = isPositive
            ? theme.palette.success.main
            : theme.palette.error.main;

          return (
            <Grid item xs={12} sm={6} md={4} key={idx}>
              <Card sx={{ p: 3, height: "100%" }}>
                {/* API: stat.title, stat.value, stat.change, stat.chartData */}
                <Stack
                  direction="row"
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      {stat.title} (Monthly)
                    </Typography>
                    {stat.title === "Active Users" ? (
                      <Typography variant="h5" sx={{ mt: 0.5 }}>
                        {stat.value.today}{" "}
                        <Typography
                          variant="body2"
                          component="span"
                          color="text.secondary"
                        >
                          / {stat.value.month}
                        </Typography>
                      </Typography>
                    ) : (
                      <Typography variant="h5" sx={{ mt: 0.5 }}>
                        {stat.value}
                      </Typography>
                    )}
                  </Box>
                  <Stack direction="row" alignItems="center">
                    {isPositive ? (
                      <ArrowDropUpIcon sx={{ color: chartColor }} />
                    ) : (
                      <ArrowDropDownIcon sx={{ color: chartColor }} />
                    )}
                    <Typography variant="subtitle2" sx={{ color: chartColor }}>
                      {Math.abs(stat.change)}%
                    </Typography>
                  </Stack>
                </Stack>
                <Box sx={{ width: "100%", height: 300, mt: 2 }}>
                  <LineChart
                    height={300}
                    series={[{ data: stat.chartData, color: chartColor }]}
                    xAxis={[
                      {
                        scaleType: "point",
                        data: [
                          "Week 1",
                          "Week 2",
                          "Week 3",
                          "Week 4",
                          "Week 5",
                          "Week 6",
                        ],
                      },
                    ]}
                  />
                </Box>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      <Box textAlign="right" mt={2}>
        <Button variant="contained" onClick={() => handleOpen("userInsights")}>
          View User Insights
        </Button>
      </Box>

      {/* Live Stats Section */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <CustomBreadcrumbs
            heading="Ongoing Live Sessions"
            sx={{ mt: 4, mb: { xs: 3, md: 5 } }}
          />
          <Grid container spacing={2}>
            {liveStats.map((stat, idx) => (
              <Grid item xs={12} sm={6} md={3} key={idx}>
                <Card sx={{ p: 3, height: "100%" }}>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    {/* Left side: title and value */}
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        {stat.title}
                      </Typography>
                      <Typography variant="h5" sx={{ mt: 0.5 }}>
                        {stat.value}
                      </Typography>
                    </Box>

                    {/* Right side: mini bar chart */}
                    <Box sx={{ width: 80, height: 40 }}>
                      <BarChart
                        xAxis={[{ scaleType: "band", data: [""] }]} // dummy x-axis
                        series={[
                          {
                            data: [stat.value], // single value bar
                            color: barColors[idx % barColors.length],
                          },
                        ]}
                        width={80}
                        height={40}
                        margin={{ top: 5, bottom: 5, left: 5, right: 5 }}
                        grid={{ vertical: false, horizontal: false }}
                        axis={{
                          left: null,
                          bottom: null,
                        }}
                      />
                    </Box>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>

          <Box textAlign="right" mt={2}>
            <Button
              variant="contained"
              sx={{ mt: 3 }}
              onClick={() => handleOpen("liveAnalytics")}
            >
              View All Live Analytics
            </Button>
          </Box>
        </Grid>
      </Grid>

      {/* Donations Section */}
      <CustomBreadcrumbs
        heading="Donations (Current Month)"
        sx={{ mt: 4, mb: { xs: 3, md: 5 } }}
      />
      <Grid container spacing={3}>
        {donationStats.map((stat, idx) => (
          <Grid item xs={12} sm={6} md={4} key={idx}>
            <Card sx={{ p: 3, height: "100%" }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                {/* Left: Title + Value */}
                <Box>
                  <Typography variant="subtitle2" color="text.secondary">
                    {stat.title}
                  </Typography>
                  <Typography variant="h5" sx={{ mt: 0.5 }}>
                    {stat.value}
                  </Typography>
                </Box>

                {/* Right: Mini Bar Chart */}
                <Box sx={{ width: 80, height: 40 }}>
                  <BarChart
                    xAxis={[{ scaleType: "band", data: [""] }]} // dummy x-axis
                    series={[
                      {
                        data: [stat.value], // single value bar
                        color: barColors[idx % barColors.length],
                      },
                    ]}
                    width={80}
                    height={40}
                    margin={{ top: 5, bottom: 5, left: 5, right: 5 }}
                    grid={{ vertical: false, horizontal: false }}
                    axis={{
                      left: null,
                      bottom: null,
                    }}
                  />
                </Box>
              </Box>
            </Card>
          </Grid>
        ))}
        {/* Campaigns and Revenue Bar Chart */}
        <Grid item xs={12}>
          <Card sx={{ p: 3 }}>
            <Typography variant="subtitle1" mb={2}>
              Top Campaigns
            </Typography>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Campaign Name</TableCell>
                  <TableCell>Amount Raised</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {topCampaigns.map((campaign, idx) => (
                  <TableRow key={idx}>
                    {/* API: campaign.name, campaign.amount */}
                    <TableCell>{campaign.name}</TableCell>
                    <TableCell>{campaign.amount}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <Box sx={{ width: "100%", height: 300, mt: 3 }}>
              <BarChart
                height={300}
                xAxis={[{ scaleType: "band", data: revenueLabels }]}
                series={[{ data: revenueTrend, color: "green" }]}
              />
            </Box>

            <Box textAlign="right" mt={2}>
              <Button
                variant="contained"
                onClick={() => handleOpen("donationReports")}
              >
                Explore Donation Reports
              </Button>
            </Box>
          </Card>
        </Grid>
        {/* Modal implementation */}
        <Modal
          open={openModal !== null}
          onClose={handleClose}
          sx={{ maxHeight: "600px", overflow: "scroll" }}
        >
          <Fade in={openModal !== null}>
            <Paper
              sx={{
                maxWidth: 700,
                margin: "auto",
                mt: 10,
                p: 3,
                outline: "none",
              }}
            >
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                mb={2}
              >
                <Typography variant="h6">
                  {openModal === "userInsights" && "User Insights"}
                  {openModal === "liveAnalytics" && "Live Analytics"}
                  {openModal === "donationReports" && "Donation Reports"}
                </Typography>
                <IconButton onClick={handleClose}>
                  <CloseIcon />
                </IconButton>
              </Box>

              {/* Render modal content conditionally */}

              {openModal === "userInsights" && (
                <TableContainer component={Paper}>
                  <Table size="small" aria-label="user insights table">
                    <TableHead>
                      <TableRow>
                        <TableCell>Title</TableCell>
                        <TableCell>Value</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {statsData.map((stat, idx) => (
                        <TableRow key={idx}>
                          <TableCell>{stat.title}</TableCell>
                          <TableCell>
                            {typeof stat.value === "object"
                              ? stat.value.today
                              : stat.value}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}

              {openModal === "liveAnalytics" && (
                <TableContainer component={Paper}>
                  <Table size="small" aria-label="live analytics table">
                    <TableHead>
                      <TableRow>
                        <TableCell>Title</TableCell>
                        <TableCell>Value</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {liveStats.map((stat, idx) => (
                        <TableRow key={idx}>
                          <TableCell>{stat.title}</TableCell>
                          <TableCell>{stat.value}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}

              {openModal === "donationReports" && (
                <Box>
                  <Typography variant="subtitle1" mb={1}>
                    Donation Stats
                  </Typography>
                  <TableContainer component={Paper} sx={{ mb: 3 }}>
                    <Table size="small" aria-label="donation stats table">
                      <TableHead>
                        <TableRow>
                          <TableCell>Title</TableCell>
                          <TableCell>Value</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {donationStats.map((stat, idx) => (
                          <TableRow key={idx}>
                            <TableCell>{stat.title}</TableCell>
                            <TableCell>{stat.value}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  <Typography variant="subtitle1" mb={1}>
                    Top Campaigns
                  </Typography>
                  {topCampaigns.length === 0 ? (
                    <Typography>No campaigns available.</Typography>
                  ) : (
                    <TableContainer component={Paper}>
                      <Table size="small" aria-label="top campaigns table">
                        <TableHead>
                          <TableRow>
                            <TableCell>Campaign Name</TableCell>
                            <TableCell>Amount</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {topCampaigns.map((campaign, idx) => (
                            <TableRow key={idx}>
                              <TableCell>{campaign.name}</TableCell>
                              <TableCell>${campaign.amount}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                </Box>
              )}
            </Paper>
          </Fade>
        </Modal>
        ;
      </Grid>
    </DashboardContent>
  );
}
