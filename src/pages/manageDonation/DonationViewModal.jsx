import React from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Stack,
  MenuItem,
} from "@mui/material";

export default function DonationViewModal({ open, onClose, donation }) {
  if (!donation) return null;

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle>View Fundraising Event</DialogTitle>
      <DialogContent dividers>
        <Stack spacing={2} mt={1}>
          <TextField
            label="Title"
            fullWidth
            value={donation.title}
            InputProps={{ readOnly: true }}
          />
          <TextField
            label="Goal"
            fullWidth
            value={donation.goal}
            InputProps={{ readOnly: true }}
          />
          <TextField
            label="Raised"
            fullWidth
            value={donation.raised || "-"}
            InputProps={{ readOnly: true }}
          />
          <TextField
            label="Start Date"
            fullWidth
            value={donation.startDate}
            InputProps={{ readOnly: true }}
          />
          <TextField
            label="End Date"
            fullWidth
            value={donation.endDate}
            InputProps={{ readOnly: true }}
          />
          <TextField
            label="Status"
            fullWidth
            value={donation.status || "-"}
            InputProps={{ readOnly: true }}
          />
        </Stack>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
}
