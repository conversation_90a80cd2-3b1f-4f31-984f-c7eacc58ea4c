import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  <PERSON>alogContent,
  <PERSON>alog<PERSON><PERSON>,
  <PERSON>ton,
  Typography,
} from "@mui/material";

// Helper to convert array of objects to CSV string and trigger download
function downloadCSV(data, filename = "data.csv") {
  if (!data || !data.length) {
    alert("No data to download");
    return;
  }

  const headers = Object.keys(data[0]);
  const csvRows = [
    headers.join(","),
    ...data.map((row) =>
      headers
        .map((fieldName) => {
          const cell = row[fieldName] ?? "";
          const escaped = cell.toString().replace(/"/g, '""');
          return `"${escaped}"`;
        })
        .join(","),
    ),
  ];

  const csvString = csvRows.join("\n");
  const blob = new Blob([csvString], { type: "text/csv" });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  a.click();
  window.URL.revokeObjectURL(url);
}

export default function DonationDownloadModal({ open, onClose, donation }) {
  // Here donation is one event object — but we want to download the entire table?

  // You can either:
  // 1. Pass the full donationsData array from parent as a prop and download it here, or
  // 2. Download only the single donation event data passed here (like details for that row)

  // For full table CSV download, you'd pass full data array as a prop.
  // Let's assume you want to download only the selected donation event data as CSV (single row)

  const handleDownload = () => {
    // Wrap single donation in array for CSV generation
    downloadCSV([donation], `donation-${donation.eventId}.csv`);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="xs">
      <DialogTitle>Download Donation Data</DialogTitle>
      <DialogContent dividers>
        <Typography>
          Are you sure you want to download data for{" "}
          <strong>{donation?.title}</strong> (ID: {donation?.eventId})?
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button variant="contained" onClick={handleDownload}>
          Download CSV
        </Button>
      </DialogActions>
    </Dialog>
  );
}
