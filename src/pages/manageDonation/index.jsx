import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Stack,
  Grid,
  Box,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DownloadIcon from "@mui/icons-material/Download";
import BarChartIcon from "@mui/icons-material/BarChart";
import { useGetDonationsMutation } from "../../services/donationsApi";
import DonationEditModal from "./DonationEditModal";
import DonationViewModal from "./DonationViewModal";
import DonationDownloadModal from "./DonationDownloadModal";
import { CustomBreadcrumbs } from "../../components/custom-breadcrumbs";
import { paths } from "../../routes/paths";
import { DashboardContent } from "../../layout/dashboard/content";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Line,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
} from "recharts";

const ManageDonation = () => {
  const [getDonations] = useGetDonationsMutation();
  const [donationsData, setDonationsData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);

  const fetchData = async () => {
    try {
      const result = await getDonations().unwrap();
      console.log("Fetched donations after edit:", result);
      setDonationsData(result);
      setIsLoading(false);
      setIsError(false);
    } catch (error) {
      setIsError(true);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const [openModal, setOpenModal] = useState(null);
  const [selectedDonation, setSelectedDonation] = useState(null);

  const handleOpen = (modalType, donation) => {
    setSelectedDonation(donation);
    setOpenModal(modalType);
  };

  const handleClose = () => {
    setOpenModal(null);
    setSelectedDonation(null);
  };

  const summary = donationsData?.data?.data?.summary || {};
  const donationEvents = donationsData?.data?.data?.donationEvents || [];

  const getSparkline = (type) => {
    // Use static example data or later replace with real trends from API if you want
    const dataMap = {
      goal: [10000, 15000, 20000, 22000, 25000],
      raised: [8000, 12000, 18000, 30000, 54000],
      pending: [12000, 10000, 9000, 11000, 11500],
    };
    const data = dataMap[type].map((val, index) => ({
      name: index,
      value: val,
    }));

    return (
      <ResponsiveContainer width={80} height={40}>
        <BarChart data={data}>
          <Bar dataKey="value" fill="green" radius={[3, 3, 0, 0]} barSize={8} />
        </BarChart>
      </ResponsiveContainer>
    );
  };

  const fundraisingSummary = [
    {
      title: "Total Goal",
      value: `$${summary.totalGoal?.toLocaleString() || "0"}`,
      chart: getSparkline("goal"),
    },
    {
      title: "Total Raised",
      value: `$${summary.totalRaised?.toLocaleString() || "0"}`,
      chart: getSparkline("raised"),
    },
    {
      title: "Total Pending",
      value: `$${summary.totalPending?.toLocaleString() || "0"}`,
      chart: getSparkline("pending"),
    },
  ];

  return (
    <DashboardContent
      sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}
    >
      <CustomBreadcrumbs
        heading="Manage Donation"
        links={[
          { name: "Dashboard", href: paths.main.root },
          { name: "Fundraising" },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Grid container spacing={2} mb={2}>
        {fundraisingSummary.map((summary, idx) => (
          <Grid item xs={12} sm={6} md={4} key={idx}>
            <Card sx={{ height: "100%" }}>
              <CardContent>
                <Box
                  display="flex"
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Box flexGrow={1}>
                    <Typography variant="subtitle2" gutterBottom>
                      {summary.title}
                    </Typography>
                    <Typography variant="h5" fontWeight="bold">
                      {summary.value}
                    </Typography>
                  </Box>
                  <Box>{summary.chart}</Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Card>
        <CardHeader title="Fundraising Events" />
        <CardContent>
          {isLoading ? (
            <Typography>Loading events...</Typography>
          ) : isError ? (
            <Typography color="error">Failed to load donations.</Typography>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>ID</TableCell>
                    <TableCell>Title</TableCell>
                    <TableCell>Organizer</TableCell>
                    <TableCell>Goal</TableCell>
                    <TableCell>Raised</TableCell>
                    <TableCell>Pending</TableCell>
                    <TableCell>Start Date</TableCell>
                    <TableCell>End Date</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {donationEvents.map((event) => {
                    const pending = event.goal - event.raised;
                    return (
                      <TableRow key={event.eventId}>
                        <TableCell>{event.eventId}</TableCell>
                        <TableCell>{event.title}</TableCell>
                        <TableCell>{event.organizer}</TableCell>
                        <TableCell>${event.goal.toLocaleString()}</TableCell>
                        <TableCell>${event.raised.toLocaleString()}</TableCell>
                        <TableCell>${pending.toLocaleString()}</TableCell>
                        <TableCell>{event.startDate}</TableCell>
                        <TableCell>{event.endDate}</TableCell>
                        <TableCell>{event.status}</TableCell>
                        <TableCell>
                          <Stack direction="row" spacing={1}>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => handleOpen("edit", event)}
                            >
                              <EditIcon fontSize="small" />
                            </Button>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => handleOpen("view", event)}
                            >
                              <VisibilityIcon fontSize="small" />
                            </Button>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => handleOpen("download", event)}
                            >
                              <DownloadIcon fontSize="small" />
                            </Button>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Modals */}
      {openModal === "edit" && (
        <DonationEditModal
          open={true}
          onClose={handleClose}
          donation={selectedDonation}
          onSuccess={fetchData}
        />
      )}

      {openModal === "view" && (
        <DonationViewModal
          open={true}
          onClose={handleClose}
          donation={selectedDonation}
        />
      )}
      {openModal === "download" && (
        <DonationDownloadModal
          open={true}
          onClose={handleClose}
          donation={selectedDonation}
        />
      )}
    </DashboardContent>
  );
};

export default ManageDonation;
