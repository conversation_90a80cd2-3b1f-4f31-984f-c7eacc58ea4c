import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Stack,
  Snackbar,
  Alert,
} from "@mui/material";
import { useEditDonationMutation } from "../../services/donationsApi";

export default function DonationEditModal({
  open,
  onClose,
  donation,
  onSuccess,
}) {
  const [formData, setFormData] = useState({
    eventId: "",
    title: "",
    goal: "",
    startDate: "",
    endDate: "",
  });

  const [editDonation] = useEditDonationMutation();
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  // Fill formData when donation prop changes
  useEffect(() => {
    if (donation) {
      setFormData({
        eventId: donation.eventId,
        title: donation.title,
        goal: donation.goal,
        startDate: donation.startDate,
        endDate: donation.endDate,
      });
    }
  }, [donation]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    try {
      await editDonation(formData).unwrap();
      setSnackbar({
        open: true,
        message: "Donation updated successfully!",
        severity: "success",
      });

      // Delay close and parent refresh to allow user to see feedback
      setTimeout(() => {
        if (onSuccess) onSuccess();
        onClose();
      }, 1000);
    } catch (err) {
      console.error("Edit Failed:", err);
      setSnackbar({
        open: true,
        message: "Failed to update donation.",
        severity: "error",
      });
    }
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
        <DialogTitle>Edit Fundraising Event</DialogTitle>
        <DialogContent dividers>
          <Stack spacing={2} mt={1}>
            <TextField
              label="Title"
              name="title"
              fullWidth
              value={formData.title}
              onChange={handleChange}
            />
            <TextField
              label="Goal"
              name="goal"
              type="number"
              fullWidth
              value={formData.goal}
              onChange={handleChange}
            />
            <TextField
              label="Start Date"
              name="startDate"
              type="date"
              fullWidth
              value={formData.startDate}
              onChange={handleChange}
              InputLabelProps={{ shrink: true }}
            />
            <TextField
              label="End Date"
              name="endDate"
              type="date"
              fullWidth
              value={formData.endDate}
              onChange={handleChange}
              InputLabelProps={{ shrink: true }}
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button variant="contained" onClick={handleSave}>
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          severity={snackbar.severity}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
}
