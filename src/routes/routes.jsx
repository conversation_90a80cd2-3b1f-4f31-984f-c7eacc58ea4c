import { SvgColor } from "../components/svg-color";
import { paths } from "./paths";

const icon = (name) => (
  <SvgColor
    src={new URL(`../assets/icons/${name}.svg`, import.meta.url).href}
  />
);

const ICONS = {
  dashboard: icon("ic-dashboard"),
  analytics: icon("ic-analytics"),
  user: icon("ic-user"),
  ads: icon("ic-ads"),
  customer: icon("ic-customer"),
  download: icon("ic-download"),
  settings: icon("ic-settings"),
  userAnalytics: icon("ic-user-analytics"),
  notifications: icon("ic-notifications"),
  adsReport: icon("ic-ad-report"),
  donation: icon("ic-manage-donation"),
  cms: icon("ic-content"),
};

export const navData = [
  {
    subheader: "Overview",
    items: [
      { title: "Home", path: paths.main.root, icon: ICONS.dashboard },
      {
        title: "Admin User",
        path: paths.main.user.list,
        icon: ICONS.user,
      },
      {
        title: "Ads",
        path: paths.main.ads.list,
        icon: ICONS.ads,
      },
      {
        title: "Customer",
        path: paths.main.customer.list,
        icon: ICONS.customer,
      },
      {
        title: "Manage Donation",
        path: paths.main.ManageDonation,
        icon: ICONS.donation,
      },
      {
        title: "CMS",
        path: paths.main.cms.list,
        icon: ICONS.cms,
      },
    ],
  },
  {
    subheader: "Analytics and Report",
    items: [
      {
        title: "Live Metrics",
        path: paths.main.analytics.liveMetrics,
        icon: ICONS.analytics,
      },
      {
        title: "User Analytics",
        path: paths.main.analytics.userAnalytics,
        icon: ICONS.userAnalytics,
      },
      {
        title: "Download Report",
        path: paths.main.analytics.downloadReport,
        icon: ICONS.download,
      },
      {
        title: "Ad Analytics Report",
        path: paths.main.analytics.adAnalyticsReport,
        icon: ICONS.adsReport,
      },
    ],
  },
  {
    subheader: "",
    items: [
      {
        title: "Settings",
        path: paths.main.settings,
        icon: ICONS.settings,
      },
    ],
  },
];
