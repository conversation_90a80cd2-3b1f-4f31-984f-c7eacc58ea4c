import { lazy, Suspense } from "react";
import { Layout } from "../layout/layout";
import { LoadingScreen } from "../components/loading-screen";
import { usePathname } from "../components/routes/hooks";
import { Outlet } from "react-router";
import { AuthLayout } from "../layout/auth-layout";
import { Navigate } from "react-router";
import useAuth from "../utils/useAuth";

// Auth
const SignInPage = lazy(() => import("../pages/auth/signIn"));
const ForgotPassword = lazy(() => import("../pages/auth/forgotPassword"));
const ResetPassword = lazy(() => import("../pages/auth/resetPassword"));
// Dashboard
const Dashboard = lazy(() => import("../pages/dashboard"));
// Users
const UserList = lazy(() => import("../pages/user"));
const UserView = lazy(() => import("../pages/user/view.jsx"));
const UserCreate = lazy(() => import("../pages/user/create.jsx"));
const UserEdit = lazy(() => import("../pages/user/edit.jsx"));
// Customers
const CustomerList = lazy(() => import("../pages/customer"));
const CustomerView = lazy(() => import("../pages/customer/view.jsx"));
const CustomerCreate = lazy(() => import("../pages/customer/create.jsx"));
const CustomerEdit = lazy(() => import("../pages/customer/edit.jsx"));
//Manage Donation
const ManageDonation = lazy(() => import("../pages/manageDonation"));
//Manage CMS
const CmsList = lazy(() => import("../pages/cms"));
const CmsView = lazy(() => import("../pages/cms/view"));
const CmsCreate = lazy(() => import("../pages/cms/create"));
const CmsEdit = lazy(() => import("../pages/cms/edit"));
// Ads
const AdsList = lazy(() => import("../pages/ads"));
const AdsView = lazy(() => import("../pages/ads/view.jsx"));
const AdsCreate = lazy(() => import("../pages/ads/create.jsx"));
const AdsEdit = lazy(() => import("../pages/ads/edit.jsx"));
// 404 page
const Page404 = lazy(() => import("../pages/error"));
// Profile
const MyProfile = lazy(() => import("../pages/myProfile"));
const ChangePassword = lazy(() => import("../pages/changePassword"));
// Analytics
const LiveMetrics = lazy(() => import("../pages/liveMetrices"));
const UserAnalytics = lazy(() => import("../pages/userAnalytics"));
const DownloadReport = lazy(() => import("../pages/downloadReports"));
const AdAnalyticsReport = lazy(() => import("../pages/AdAnalyticsReport"));
// Notifications
const Notification = lazy(() => import("../pages/notification"));
const ViewNotification = lazy(() => import("../pages/notification/view.jsx"));
const EditNotification = lazy(() => import("../pages/notification/edit.jsx"));
// Settings
const SettingsManagement = lazy(() => import("../pages/setings"));

function SuspenseOutlet() {
  const pathname = usePathname();
  return (
    <Suspense key={pathname} fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  );
}

const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return isAuthenticated ? children : <Navigate to="/auth/sign-in" replace />;
};

const layout = () => (
  <ProtectedRoute>
    <Layout>
      <SuspenseOutlet />
    </Layout>
  </ProtectedRoute>
);
export const routes = [
  {
    path: "auth",
    element: (
      <Suspense fallback={<LoadingScreen />}>
        <Outlet />
      </Suspense>
    ),
    children: [
      {
        path: "sign-in",
        element: (
          <AuthLayout imageUrl={"/assets/login.png"}>
            <SignInPage />
          </AuthLayout>
        ),
      },
      {
        path: "forgot-password",
        element: (
          <AuthLayout imageUrl={"/assets/forgot.png"}>
            <ForgotPassword />
          </AuthLayout>
        ),
      },
      {
        path: "reset-password",
        element: (
          <AuthLayout imageUrl={"/assets/reset.png"}>
            <ResetPassword />
          </AuthLayout>
        ),
      },
    ],
  },
  {
    path: "/",
    element: layout(),
    children: [
      { index: true, element: <Dashboard /> },
      {
        path: "user",
        children: [
          { index: true, element: <UserList /> },
          { path: "list", element: <UserList /> },
          { path: ":id", element: <UserView /> },
          { path: "create", element: <UserCreate /> },
          { path: "edit/:id", element: <UserEdit /> },
        ],
      },
      {
        path: "ads",
        children: [
          { index: true, element: <AdsList /> },
          { path: "list", element: <AdsList /> },
          { path: ":id", element: <AdsView /> },
          { path: "create", element: <AdsCreate /> },
          { path: "edit/:id", element: <AdsEdit /> },
        ],
      },
      {
        path: "customer",
        children: [
          { index: true, element: <CustomerList /> },
          { path: "list", element: <CustomerList /> },
          { path: ":id", element: <CustomerView /> },
          { path: "create", element: <CustomerCreate /> },
          { path: "edit/:id", element: <CustomerEdit /> },
        ],
      },
      {
        path: "manage-donation",
        children: [
          { index: true, element: <ManageDonation /> },
          { path: "list", element: <ManageDonation /> },
        ],
      },
      {
        path: "cms",
        children: [
          { index: true, element: <CmsList /> },
          { path: "list", element: <CmsList /> },
          { path: ":id", element: <CmsEdit /> },
          { path: "create", element: <CmsCreate /> },
          { path: "edit/:id", element: <CmsView /> },
        ],
      },
      {
        path: "live-metrics",
        children: [
          { index: true, element: <LiveMetrics /> },
          { path: "list", element: <LiveMetrics /> },
        ],
      },
      {
        path: "user-analytics",
        children: [
          { index: true, element: <UserAnalytics /> },
          { path: "list", element: <UserAnalytics /> },
        ],
      },
      {
        path: "download-report",
        children: [
          { index: true, element: <DownloadReport /> },
          { path: "list", element: <DownloadReport /> },
        ],
      },
      {
        path: "ad-analytics-report",
        children: [
          { index: true, element: <AdAnalyticsReport /> },
          { path: "list", element: <AdAnalyticsReport /> },
        ],
      },
      {
        path: "my-profile",
        children: [
          { index: true, element: <MyProfile /> },
          { path: "", element: <MyProfile /> },
        ],
      },
      {
        path: "change-password",
        children: [
          { index: true, element: <ChangePassword /> },
          { path: "", element: <ChangePassword /> },
        ],
      },
      {
        path: "settings",
        children: [
          { index: true, element: <SettingsManagement /> },
          { path: "", element: <SettingsManagement /> },
        ],
      },
    ],
  },
  { path: "*", element: <Page404 /> },
];
