import { useEffect } from "react";
import { mergeClasses } from "minimal-shared/utils";
import Box from "@mui/material/Box";
import Drawer from "@mui/material/Drawer";
import { usePathname } from "../components/routes/hooks";
import { Scrollbar } from "../components/scrollbar";
import { NavSectionVertical } from "../components/nav-section";
import { layoutClasses } from "./core-classes";
import pipaanImage from "../assets/pipaan.png";

export function NavMobile({
  data,
  open,
  onClose,
  slots,
  sx,
  className,
  ...other
}) {
  const pathname = usePathname();

  useEffect(() => {
    if (open) {
      onClose();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  return (
    <Drawer
      open={open}
      onClose={onClose}
      PaperProps={{
        className: mergeClasses([
          layoutClasses.nav.root,
          layoutClasses.nav.vertical,
          className,
        ]),
        sx: [
          (theme) => ({
            overflow: "unset",
            bgcolor: "var(--layout-nav-bg)",
            width: "var(--layout-nav-mobile-width)",
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ],
      }}
    >
      {slots?.topArea ?? (
        <Box sx={{ pl: 3.5, pt: 2.5, pb: 1 }}>
          <img src={pipaanImage} alt="Pippan Logo" width={100} height={50} />
        </Box>
      )}

      <Scrollbar fillContent>
        <NavSectionVertical
          data={data}
          sx={{ px: 2, flex: "1 1 auto" }}
          {...other}
        />
      </Scrollbar>

      {slots?.bottomArea}
    </Drawer>
  );
}
