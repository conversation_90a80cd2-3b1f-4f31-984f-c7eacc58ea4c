import { Box } from "@mui/material";
import Avatar from "@mui/material/Avatar";
import IconButton from "@mui/material/IconButton";

export function AccountButton({ photoURL, displayName, sx, ...other }) {
  return (
    <IconButton
      aria-label="Account button"
      sx={[{ p: 0 }, ...(Array.isArray(sx) ? sx : [sx])]}
      {...other}
    >
      <Box
        sx={[
          {
            minWidth: 40,
            minHeight: 40,
            overflow: "hidden",
            position: "relative",
            p: "3px",
            borderRadius: "50%",
            width: 40,
            height: 40,
          },
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
      >
        <Avatar src={photoURL} alt={displayName} sx={{ width: 1, height: 1 }}>
          {displayName?.charAt(0).toUpperCase()}
        </Avatar>
      </Box>
    </IconButton>
  );
}
