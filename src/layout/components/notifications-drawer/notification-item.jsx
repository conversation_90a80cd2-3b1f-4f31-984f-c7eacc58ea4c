import Box from "@mui/material/Box";
import Avatar from "@mui/material/Avatar";
import ListItemText from "@mui/material/ListItemText";
import ListItemAvatar from "@mui/material/ListItemAvatar";
import ListItemButton from "@mui/material/ListItemButton";
import { fToNow } from "../../../utils/format-time";

const readerContent = (data) => (
  <Box
    dangerouslySetInnerHTML={{ __html: data }}
    sx={{
      "& p": { m: 0, typography: "body2" },
      "& a": { color: "inherit", textDecoration: "none" },
      "& strong": { typography: "subtitle2" },
    }}
  />
);

export function NotificationItem({ notification }) {
  const renderAvatar = () => (
    <ListItemAvatar>
      {!notification.avatarUrl ? (
        <Avatar
          src={notification.avatarUrl}
          sx={{ bgcolor: "background.neutral" }}
        />
      ) : (
        <Box
          sx={{
            width: 40,
            height: 40,
            display: "flex",
            borderRadius: "50%",
            alignItems: "center",
            justifyContent: "center",
            bgcolor: "background.neutral",
          }}
        >
          <Box
            component="img"
            src={`/assets/icons/notification/${(notification.type === "order" && "ic-order") || (notification.type === "chat" && "ic-chat") || (notification.type === "mail" && "ic-mail") || (notification.type === "delivery" && "ic-delivery")}.svg`}
            sx={{ width: 24, height: 24 }}
          />
        </Box>
      )}
    </ListItemAvatar>
  );

  const renderText = () => (
    <ListItemText
      primary={readerContent(notification.title)}
      secondary={
        <>
          {fToNow(notification.createdAt)}
          <Box
            component="span"
            sx={{
              width: 2,
              height: 2,
              borderRadius: "50%",
              bgcolor: "currentColor",
            }}
          />
          {notification.category}
        </>
      }
      slotProps={{
        primary: {
          sx: { mb: 0.5 },
        },
        secondary: {
          sx: {
            gap: 0.5,
            display: "flex",
            alignItems: "center",
            typography: "caption",
            color: "text.disabled",
          },
        },
      }}
    />
  );

  const renderUnReadBadge = () =>
    notification.isUnRead && (
      <Box
        sx={{
          top: 26,
          width: 8,
          height: 8,
          right: 20,
          borderRadius: "50%",
          bgcolor: "info.main",
          position: "absolute",
        }}
      />
    );

  return (
    <ListItemButton
      disableRipple
      sx={[
        (theme) => ({
          p: 2.5,
          alignItems: "flex-start",
          borderBottom: `dashed 1px ${theme.vars.palette.divider}`,
        }),
      ]}
    >
      {renderUnReadBadge()}
      {renderAvatar()}

      <Box sx={{ minWidth: 0, flex: "1 1 auto" }}>{renderText()}</Box>
    </ListItemButton>
  );
}
