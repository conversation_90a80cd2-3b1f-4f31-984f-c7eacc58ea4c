import { useBoolean } from "minimal-shared/hooks";
import { paths } from "../../routes/paths";
import { Label } from "../../components/label";
import { Iconify } from "../../components/iconify";
import { Scrollbar } from "../../components/scrollbar";
import { AccountButton } from "./account-button";
import { SignOutButton } from "./sign-out-button";
import {
  Box,
  Link,
  Avatar,
  Drawer,
  MenuList,
  MenuItem,
  Typography,
  IconButton,
} from "@mui/material";
import { RouterLink } from "../../components/routes/components";

export function AccountDrawer({ data = [], sx, ...other }) {
  const { user } = {};
  const { value: open, onFalse: onClose, onTrue: onOpen } = useBoolean();

  const renderAvatar = () => (
    <Box
      sx={[
        {
          minWidth: 40,
          minHeight: 40,
          overflow: "hidden",
          position: "relative",
          p: "3px",
          borderRadius: "50%",
          width: 40,
          height: 40,
        },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
    >
      <Avatar
        src={user?.photoURL}
        alt={user?.displayName}
        sx={{ width: 1, height: 1 }}
      >
        {user?.displayName?.charAt(0).toUpperCase()}
      </Avatar>
    </Box>
  );

  const renderList = () => (
    <MenuList
      disablePadding
      sx={[
        (theme) => ({
          py: 3,
          px: 2.5,
          borderTop: `dashed 1px ${theme.vars.palette.divider}`,
          borderBottom: `dashed 1px ${theme.vars.palette.divider}`,
          "& li": { p: 0 },
        }),
      ]}
    >
      {data.map((option) => {
        const rootLabel = "Home";
        const rootHref = paths.main.root;

        return (
          <MenuItem key={option.label}>
            <Link
              component={RouterLink}
              href={option.label === "Home" ? rootHref : option.href}
              color="inherit"
              underline="none"
              onClick={onClose}
              sx={{
                p: 1,
                width: 1,
                display: "flex",
                typography: "body2",
                alignItems: "center",
                color: "text.secondary",
                "& svg": { width: 24, height: 24 },
                "&:hover": { color: "text.primary" },
              }}
            >
              {option.icon}

              <Box component="span" sx={{ ml: 2 }}>
                {option.label === "Home" ? rootLabel : option.label}
              </Box>

              {option.info && (
                <Label color="error" sx={{ ml: 1 }}>
                  {option.info}
                </Label>
              )}
            </Link>
          </MenuItem>
        );
      })}
    </MenuList>
  );

  return (
    <>
      <AccountButton
        onClick={onOpen}
        photoURL={user?.photoURL}
        displayName={user?.displayName}
        sx={sx}
        {...other}
      />

      <Drawer
        open={open}
        onClose={onClose}
        anchor="right"
        slotProps={{ backdrop: { invisible: true } }}
        PaperProps={{ sx: { width: 320 } }}
      >
        <IconButton
          onClick={onClose}
          sx={{
            top: 12,
            left: 12,
            zIndex: 9,
            position: "absolute",
          }}
        >
          <Iconify icon="mingcute:close-line" />
        </IconButton>

        <Scrollbar>
          <Box
            sx={{
              pt: 8,
              display: "flex",
              alignItems: "center",
              flexDirection: "column",
            }}
          >
            {renderAvatar()}

            <Typography variant="subtitle1" noWrap sx={{ mt: 2 }}>
              {user?.displayName}
            </Typography>

            <Typography
              variant="body2"
              sx={{ color: "text.secondary", mt: 0.5 }}
              noWrap
            >
              {user?.email}
            </Typography>
          </Box>

          {renderList()}
        </Scrollbar>

        <Box sx={{ p: 2.5 }}>
          <SignOutButton onClose={onClose} />
        </Box>
      </Drawer>
    </>
  );
}
