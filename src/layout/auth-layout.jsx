import { Box, Typography } from "@mui/material";
import { LayoutSection } from "./core/layout-section";
import { MainSection } from "./main-section";

export function AuthLayout({
  imageUrl,
  sx,
  cssVars,
  children,
  slotProps,
  layoutQuery = "md",
}) {
  const renderMain = () => (
    <MainSection
      {...slotProps?.main}
      sx={[
        (theme) => ({
          flexDirection: "column",
          height: "100vh",
          overflow: "hidden",
          [theme.breakpoints.up(layoutQuery)]: { flexDirection: "row" },
        }),
        ...(Array.isArray(slotProps?.main?.sx)
          ? (slotProps?.main?.sx ?? [])
          : [slotProps?.main?.sx]),
      ]}
    >
      <Box
        sx={[
          (theme) => ({
            m: 3,
            width: 1,
            display: "none",
            position: "relative",
            [theme.breakpoints.up(layoutQuery)]: {
              width: "50%",
              display: "flex",
              alignItems: "center",
              flexDirection: "column",
              justifyContent: "center",
            },
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
      >
        <Box
          component="img"
          alt="Dashboard illustration"
          src={imageUrl}
          sx={{
            maxWidth: "90%",
            maxHeight: "90%",
            width: "auto",
            height: "auto",
          }}
        />

        <div>
          <Typography
            sx={[
              (theme) => ({
                color: theme.vars.palette.common.green,
                fontSize: "16px",
                px: 2,
              }),
              ...(Array.isArray(sx) ? sx : [sx]),
            ]}
          >
            © 2025 Pipaan. All Rights Reserved
          </Typography>
        </div>
      </Box>
      <Box
        sx={[
          (theme) => ({
            display: "flex",
            flex: "1 1 auto",
            alignItems: "center",
            flexDirection: "column",
            backgroundColor: "#F4F4F4",
            p: theme.spacing(3, 2, 10, 2),
            [theme.breakpoints.up(layoutQuery)]: {
              justifyContent: "center",
            },
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
      >
        <Box
          sx={{
            width: 1,
            display: "flex",
            flexDirection: "column",
            maxWidth: "var(--layout-auth-content-width)",
          }}
        >
          {children}
        </Box>
      </Box>
    </MainSection>
  );

  return (
    <LayoutSection
      cssVars={{ "--layout-auth-content-width": "420px", ...cssVars }}
      sx={sx}
    >
      {renderMain()}
    </LayoutSection>
  );
}
