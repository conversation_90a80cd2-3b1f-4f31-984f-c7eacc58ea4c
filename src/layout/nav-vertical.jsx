import { varAlpha, mergeClasses } from "minimal-shared/utils";
import Box from "@mui/material/Box";
import { styled } from "@mui/material/styles";
import { NavSectionMini, NavSectionVertical } from "../components/nav-section";
import { layoutClasses } from "./core-classes";
import { NavToggleButton } from "./components/nav-toggle-button";
import { Scrollbar } from "../components/scrollbar";
import { Typography } from "@mui/material";
import pipaanImage from "../assets/pipaan.png";

export function NavVertical({
  sx,
  data,
  slots,
  cssVars,
  className,
  isNavMini,
  onToggleNav,
  layoutQuery = "md",
  ...other
}) {
  const renderNavVertical = () => (
    <>
      {slots?.topArea ?? (
        <Box sx={{ pl: 3.5, pt: 2.5, pb: 1 }}>
          <img src={pipaanImage} alt="Pippan Logo" width={100} height={50} />
        </Box>
      )}

      <Scrollbar fillContent sx={{ overflowY: "auto" }}>
        <NavSectionVertical
          data={data}
          cssVars={cssVars}
          sx={{ px: 2, flex: "1 1 auto" }}
        />
      </Scrollbar>

      <Typography
        sx={[
          (theme) => ({
            color: theme.vars.palette.common.green,
            fontSize: "16px",
            px: 2,
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
      >
        © 2025 Pipaan. All Rights Reserved
      </Typography>
    </>
  );

  const renderNavMini = () => (
    <>
      {slots?.topArea ?? (
        <Box sx={{ display: "flex", justifyContent: "center", py: 2.5 }}>
          <img src={pipaanImage} alt="Pippan Logo" width={100} height={50} />
        </Box>
      )}

      <NavSectionMini
        data={data}
        cssVars={cssVars}
        sx={[
          (theme) => ({
            ...theme.mixins.hideScrollY,
            pb: 2,
            px: 0.5,
            flex: "1 1 auto",
          }),
        ]}
      />

      {slots?.bottomArea}
      <Typography
        sx={[
          (theme) => ({
            color: theme.vars.palette.common.green,
            fontSize: "12px",
            px: 2,
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
      >
        © 2025 Pipaan
      </Typography>
    </>
  );

  return (
    <NavRoot
      isNavMini={isNavMini}
      layoutQuery={layoutQuery}
      className={mergeClasses([
        layoutClasses.nav.root,
        layoutClasses.nav.vertical,
        className,
      ])}
      sx={sx}
      {...other}
    >
      <NavToggleButton
        isNavMini={isNavMini}
        onClick={onToggleNav}
        sx={[
          (theme) => ({
            display: "none",
            [theme.breakpoints.up(layoutQuery)]: { display: "inline-flex" },
          }),
        ]}
      />
      {isNavMini ? renderNavMini() : renderNavVertical()}
    </NavRoot>
  );
}

const NavRoot = styled("div", {
  shouldForwardProp: (prop) =>
    !["isNavMini", "layoutQuery", "sx"].includes(prop),
})(({ isNavMini, layoutQuery = "md", theme }) => ({
  top: 0,
  left: 0,
  height: "100%",
  display: "none",
  position: "fixed",
  flexDirection: "column",
  zIndex: "var(--layout-nav-zIndex)",
  backgroundColor: theme.vars.palette.common.grey,
  width: isNavMini
    ? "var(--layout-nav-mini-width)"
    : "var(--layout-nav-vertical-width)",
  borderRight: `1px solid var(--layout-nav-border-color, ${varAlpha(theme.vars.palette.grey["500Channel"], 0.12)})`,
  transition: theme.transitions.create(["width"], {
    easing: "var(--layout-transition-easing)",
    duration: "var(--layout-transition-duration)",
  }),
  [theme.breakpoints.up(layoutQuery)]: { display: "flex" },

  "& .simplebar-placeholder": {
    height: "0px !important",
  },
}));
