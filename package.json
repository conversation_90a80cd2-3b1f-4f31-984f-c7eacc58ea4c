{"name": "pipaan-adminreact", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "prettier:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "prettier:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\""}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^4.1.3", "@iconify/react": "^5.2.0", "@mui/icons-material": "^6.4.10", "@mui/lab": "^6.0.0-beta.21", "@mui/material": "^6.3.0", "@mui/x-charts": "^7.28.0", "@mui/x-data-grid": "^7.27.3", "@mui/x-date-pickers": "^7.28.3", "@react-pdf/renderer": "^4.3.0", "@reduxjs/toolkit": "^2.6.1", "apexcharts": "^4.5.0", "axios": "^1.9.0", "dayjs": "^1.11.13", "es-toolkit": "^1.33.0", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-resources-to-backend": "^1.2.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.487.0", "minimal-shared": "^1.0.7", "react": "18", "react-apexcharts": "^1.7.0", "react-dom": "18", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.1", "react-redux": "^9.2.0", "react-router": "^7.3.0", "react-router-dom": "^7.3.0", "recharts": "^2.15.2", "simplebar-react": "^3.3.0", "sonner": "^2.0.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "prettier": "^3.5.3", "vite": "^6.2.0"}}